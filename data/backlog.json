[{"id": "DB-001", "summary": "Create gift_lists table schema", "description": "Create the gift_lists table to store collaborative gift lists for birthdays. Table should include: id (UUID, primary key), birthday_person_id (UUID, references friends.id), name (TEXT, not null), description (TEXT, nullable), created_by (UUID, references profiles.id), is_active (BO<PERSON><PERSON>N, default true), created_at (TIMESTAMP), updated_at (TIMESTAMP). Add RLS policies to ensure users can only access gift lists they created or are invited to collaborate on. Include triggers for updated_at timestamp.", "linked_tasks": [], "epic": "Database Schema", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "DB-002", "summary": "Create gift_list_collaborators table schema", "description": "Create the gift_list_collaborators table to manage collaborator permissions. Table should include: id (UUID, primary key), gift_list_id (UUID, references gift_lists.id), user_id (UUID, references profiles.id), role (TEXT, check constraint for 'ADMIN'|'COLLABORATOR'), status (TEXT, check constraint for 'PENDING'|'ACCEPTED'|'DECLINED'), invited_at (TIMESTAMP), accepted_at (TIMESTAMP, nullable), created_at (TIMESTAMP), updated_at (TIMESTAMP). Add RLS policies and unique constraint on (gift_list_id, user_id). Include triggers for updated_at timestamp.", "linked_tasks": ["DB-001"], "epic": "Database Schema", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "DB-003", "summary": "Create wish_list_items table schema", "description": "Create the wish_list_items table for birthday person's personal wish lists. Table should include: id (UUID, primary key), birthday_person_id (UUID, references friends.id), title (TEXT, not null), description (TEXT, nullable), price (DECIMAL(10,2), nullable), link (TEXT, nullable), priority (TEXT, check constraint for 'Low'|'Medium'|'High'), is_public (BO<PERSON>EAN, default false), created_at (TIMESTAMP), updated_at (TIMESTAMP). Add RLS policies to ensure only the birthday person can edit their wish list, but collaborators can view public items. Include triggers for updated_at timestamp.", "linked_tasks": [], "epic": "Database Schema", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "DB-004", "summary": "Extend wishes table for collaboration features", "description": "Add collaboration-related columns to existing wishes table: status (TEXT, check constraint for 'AVAILABLE'|'RESERVED'|'PURCHASED', default 'AVAILABLE'), reserved_by (UUID, references profiles.id, nullable), purchased_by (UUID, references profiles.id, nullable), gift_list_id (UUID, references gift_lists.id, nullable). Update existing RLS policies to handle gift list context. Create indexes on gift_list_id, reserved_by, and purchased_by for performance.", "linked_tasks": ["DB-001"], "epic": "Database Schema", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "DB-005", "summary": "Create database views for collaboration queries", "description": "Create optimized database views: 1) gift_lists_with_stats - joins gift_lists with collaborator counts, item counts, and completion status. 2) user_gift_list_participation - shows all gift lists a user is involved in with their role and status. 3) gift_list_items_with_collaborators - joins wishes with gift list and collaborator information for efficient querying. Include proper RLS on views.", "linked_tasks": ["DB-001", "DB-002", "DB-004"], "epic": "Database Schema", "priority": "Medium", "estimated_effort": "Medium", "type": "Story"}, {"id": "API-001", "summary": "Create gift list CRUD API endpoints", "description": "Parent task for implementing gift list CRUD operations. This task is broken down into sub-tasks: API-001-1 (Create operations), API-001-2 (Read operations), API-001-3 (Update operations), API-001-4 (Delete operations), and API-001-5 (Error handling and validation).", "linked_tasks": ["DB-001", "DB-005"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "API-001-1", "summary": "Implement createGiftList API endpoint", "description": "Create the `createGiftList(giftListData)` function in supabaseService.js following existing patterns.\n\n**Acceptance Criteria:**\n- Function accepts gift list data object with validation\n- Converts camelCase to snake_case for database insertion\n- Implements RLS enforcement for user permissions\n- Returns created gift list with proper error handling\n- Includes comprehensive input validation and sanitization\n\n**Implementation Details:**\n```javascript\nexport async function createGiftList(giftListData) {\n  // Validate required fields\n  // Convert camelCase to snake_case\n  // Insert into gift_lists table\n  // Handle RLS and permissions\n  // Return formatted response\n}\n```\n\n**References:** Requirements.md - Gift List Management section", "linked_tasks": ["API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "API-001-2", "summary": "Implement getGiftList and getUserGiftLists API endpoints", "description": "Create read operations for gift lists: `getGiftList(id)` and `getUserGiftLists(userId)`.\n\n**Acceptance Criteria:**\n- `getGiftList(id)` returns single gift list with collaborator info\n- `getUserGiftLists(userId)` returns paginated list of user's gift lists\n- Both functions enforce RLS policies\n- Include proper error handling for not found/unauthorized\n- Convert snake_case to camelCase in responses\n- Support filtering and sorting options\n\n**Implementation Details:**\n- Use database views for optimized queries\n- Include collaborator counts and item statistics\n- Implement pagination with limit/offset\n- Add caching considerations for performance\n\n**References:** Requirements.md - Gift Lists Overview Screen", "linked_tasks": ["API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "API-001-3", "summary": "Implement updateGiftList API endpoint", "description": "Create the `updateGiftList(id, updates)` function with proper validation and permissions.\n\n**Acceptance Criteria:**\n- Function accepts gift list ID and update object\n- Validates user permissions (admin or creator only)\n- Implements partial updates with field validation\n- <PERSON>les concurrent update conflicts\n- Returns updated gift list data\n- Triggers real-time notifications to collaborators\n\n**Implementation Details:**\n- Use optimistic locking to prevent conflicts\n- Validate each updateable field\n- Maintain audit trail of changes\n- Integrate with real-time broadcasting system\n\n**References:** Requirements.md - Gift List Management Screen", "linked_tasks": ["API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "API-001-4", "summary": "Implement deleteGiftList API endpoint", "description": "Create the `deleteGiftList(id)` function with proper cleanup and permissions.\n\n**Acceptance Criteria:**\n- Function validates user permissions (admin/creator only)\n- Implements soft delete with is_active flag\n- Handles cascade operations for related data\n- Notifies all collaborators of deletion\n- Includes confirmation mechanisms\n- Maintains data integrity\n\n**Implementation Details:**\n- Set is_active to false instead of hard delete\n- Clean up related collaborator records\n- Archive associated gift list items\n- Send notifications to all collaborators\n- Log deletion action for audit purposes\n\n**References:** Requirements.md - Gift List Management section", "linked_tasks": ["API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "API-001-5", "summary": "Implement error handling and validation for gift list APIs", "description": "Add comprehensive error handling and input validation across all gift list API endpoints.\n\n**Acceptance Criteria:**\n- Standardized error response format across all endpoints\n- Input validation with detailed error messages\n- Rate limiting implementation\n- SQL injection prevention\n- XSS protection for text fields\n- Proper HTTP status codes\n\n**Implementation Details:**\n| Validation Type | Implementation |\n|-----------------|----------------|\n| Required Fields | Check for null/undefined values |\n| Data Types | Validate string, number, boolean types |\n| String Length | Enforce min/max length constraints |\n| Special Characters | Sanitize HTML and SQL injection attempts |\n| Business Rules | Validate gift list name uniqueness per user |\n\n**Error Response Format:**\n```json\n{\n  \"error\": {\n    \"code\": \"VALIDATION_ERROR\",\n    \"message\": \"Invalid input data\",\n    \"details\": [\"Gift list name is required\"]\n  }\n}\n```", "linked_tasks": ["API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "API-002", "summary": "Create collaborator management API endpoints", "description": "Parent task for implementing collaborator management operations. This task is broken down into sub-tasks: API-002-1 (Invitation management), API-002-2 (Role management), API-002-3 (Collaborator queries), and API-002-4 (Email notification integration).", "linked_tasks": ["DB-002", "API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "API-002-1", "summary": "Implement collaborator invitation API endpoints", "description": "Create invitation management functions: `inviteCollaborator()`, `acceptInvitation()`, and `declineInvitation()`.\n\n**Acceptance Criteria:**\n- `inviteCollaborator(giftListId, userId, role)` creates pending invitation\n- `acceptInvitation(collaboratorId)` activates collaboration\n- `declineInvitation(collaboratorId)` marks invitation as declined\n- Only gift list admins can send invitations\n- Prevent duplicate invitations to same user\n- Validate user exists before sending invitation\n\n**Implementation Details:**\n```javascript\n// Invitation workflow\n1. Validate admin permissions\n2. Check for existing collaboration\n3. Create collaborator record with PENDING status\n4. Trigger email notification\n5. Return invitation details\n```\n\n**Database Operations:**\n- Insert into gift_list_collaborators table\n- Update status field for accept/decline\n- Enforce unique constraint on (gift_list_id, user_id)\n\n**References:** Requirements.md - Collaborator Invitation Screen", "linked_tasks": ["API-002"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "API-002-2", "summary": "Implement collaborator role management API endpoints", "description": "Create role management functions: `updateCollaboratorRole()` and `removeCollaborator()`.\n\n**Acceptance Criteria:**\n- `updateCollaboratorRole(collaboratorId, newRole)` changes user role\n- `removeCollaborator(collaboratorId)` removes user from gift list\n- Only admins can modify roles and remove collaborators\n- Prevent removal of last admin from gift list\n- Validate role values (ADMIN, COLLABORATOR)\n- Send notifications for role changes\n\n**Business Rules:**\n| Action | Permission Required | Validation |\n|--------|-------------------|------------|\n| Update Role | Admin only | Cannot demote last admin |\n| Remove Collaborator | Admin only | Cannot remove self if last admin |\n| Role Values | System validation | Must be ADMIN or COLLABORATOR |\n\n**Implementation Details:**\n- Check current user's admin status\n- Count existing admins before role changes\n- Update collaborator record with new role\n- Log role change for audit trail\n\n**References:** Requirements.md - Gift List Management Screen", "linked_tasks": ["API-002"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "API-002-3", "summary": "Implement collaborator query API endpoints", "description": "Create `getGiftListCollaborators(giftListId)` function with filtering and sorting capabilities.\n\n**Acceptance Criteria:**\n- Returns list of collaborators with user details\n- Includes role, status, and invitation timestamps\n- Supports filtering by role and status\n- Implements proper RLS for gift list access\n- Returns paginated results for large collaborator lists\n- Includes user profile information (name, avatar)\n\n**Response Format:**\n```json\n{\n  \"collaborators\": [\n    {\n      \"id\": \"uuid\",\n      \"user\": {\n        \"id\": \"uuid\",\n        \"name\": \"<PERSON>\",\n        \"avatar_url\": \"https://...\"\n      },\n      \"role\": \"ADMIN\",\n      \"status\": \"ACCEPTED\",\n      \"invited_at\": \"2024-01-01T00:00:00Z\",\n      \"accepted_at\": \"2024-01-01T01:00:00Z\"\n    }\n  ],\n  \"total_count\": 5,\n  \"pagination\": { \"limit\": 20, \"offset\": 0 }\n}\n```\n\n**References:** Requirements.md - Gift List Detail View", "linked_tasks": ["API-002"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "API-002-4", "summary": "Integrate email notifications with collaborator management", "description": "Add email notification triggers for all collaborator management actions using Brevo integration.\n\n**Acceptance Criteria:**\n- Send invitation emails when collaborators are invited\n- Send role change notifications to affected users\n- Send removal notifications when collaborators are removed\n- Include gift list context and birthday person details\n- Use existing Brevo email templates and patterns\n- Handle email delivery failures gracefully\n\n**Email Triggers:**\n| Action | Recipient | Template |\n|--------|-----------|----------|\n| Invitation Sent | Invited User | gift_list_invitation |\n| Invitation Accepted | Gift List Admin | invitation_accepted |\n| Role Changed | Affected User | role_changed |\n| Collaborator Removed | Removed User | collaborator_removed |\n\n**Implementation Details:**\n- Integrate with existing email service patterns\n- Queue email sending for async processing\n- Include unsubscribe options\n- Track email delivery status\n- Implement retry logic for failed sends\n\n**References:** Requirements.md - Email Notifications section", "linked_tasks": ["API-002"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "API-003", "summary": "Create wish list item API endpoints", "description": "Implement wish list item management: createWishListItem(itemData), updateWishListItem(id, updates), deleteWishListItem(id), getWishListItems(birthdayPersonId), addWishListItemToGiftList(wishListItemId, giftListId). Include privacy controls to ensure only public items are visible to collaborators. Follow existing patterns with proper validation and RLS enforcement.", "linked_tasks": ["DB-003"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "API-004", "summary": "Extend wishes API for collaboration features", "description": "Extend existing wishes API to support collaboration: reserveWishItem(wishId, userId), unreserveWishItem(wishId), markWishAsPurchased(wishId, userId), getGiftListWishes(giftListId). Update existing wish CRUD operations to handle gift_list_id context. Ensure proper permission checks for reservation and purchase actions. Add optimistic locking to prevent concurrent reservation conflicts.", "linked_tasks": ["DB-004", "API-001"], "epic": "Collaboration API", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "RT-001", "summary": "Set up Supabase Realtime channels for gift lists", "description": "Parent task for implementing real-time collaboration infrastructure. This task is broken down into sub-tasks: RT-001-1 (Channel setup), RT-001-2 (Connection management), RT-001-3 (Event subscription), and RT-001-4 (Error handling and reconnection).", "linked_tasks": ["API-001", "API-002"], "epic": "Real-time Collaboration", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "RT-001-1", "summary": "Configure Supabase Realtime channel structure", "description": "Set up the channel naming convention and subscription structure for gift list collaboration.\n\n**Acceptance Criteria:**\n- Define channel naming pattern: `gift-list:{giftListId}`\n- Configure database table subscriptions for real-time updates\n- Set up RLS policies for channel access control\n- Enable real-time on required database tables\n- Document channel structure and usage patterns\n\n**Channel Configuration:**\n```javascript\n// Channel structure\nconst channels = {\n  giftList: `gift-list:${giftListId}`,\n  tables: [\n    'gift_lists',\n    'gift_list_collaborators', \n    'wishes', // for gift list items\n    'wish_list_items'\n  ]\n}\n```\n\n**Database Tables to Enable:**\n- gift_lists (for list updates)\n- gift_list_collaborators (for collaborator changes)\n- wishes (for item status changes)\n- wish_list_items (for wish list updates)\n\n**References:** Requirements.md - Real-time Collaboration section", "linked_tasks": ["RT-001"], "epic": "Real-time Collaboration", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "RT-001-2", "summary": "Implement WebSocket connection management", "description": "Create connection state management and lifecycle handling for real-time subscriptions.\n\n**Acceptance Criteria:**\n- Implement connection state tracking (connecting, connected, disconnected)\n- Create connection manager class following existing patterns\n- Handle authentication for WebSocket connections\n- Implement connection pooling for multiple gift lists\n- Add connection health monitoring\n- Provide connection status to UI components\n\n**Connection States:**\n| State | Description | UI Indicator |\n|-------|-------------|-------------|\n| CONNECTING | Establishing connection | Loading spinner |\n| CONNECTED | Active real-time updates | Green indicator |\n| DISCONNECTED | No real-time updates | Red indicator |\n| RECONNECTING | Attempting to reconnect | Yellow indicator |\n\n**Implementation Details:**\n```javascript\nclass RealtimeConnectionManager {\n  constructor() {\n    this.connections = new Map()\n    this.connectionStates = new Map()\n  }\n  \n  connect(giftListId) { /* ... */ }\n  disconnect(giftListId) { /* ... */ }\n  getConnectionState(giftListId) { /* ... */ }\n}\n```\n\n**References:** Existing WebSocket patterns in codebase", "linked_tasks": ["RT-001"], "epic": "Real-time Collaboration", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "RT-001-3", "summary": "Implement event subscription and filtering", "description": "Create event subscription system with proper filtering and permission checking.\n\n**Acceptance Criteria:**\n- Subscribe to database changes for gift list tables\n- Filter events based on user permissions\n- Implement event type categorization\n- Handle subscription lifecycle (subscribe/unsubscribe)\n- Prevent unauthorized event access\n- Support multiple concurrent subscriptions\n\n**Event Types:**\n```javascript\nconst EVENT_TYPES = {\n  GIFT_LIST_UPDATED: 'gift_list_updated',\n  COLLABORATOR_JOINED: 'collaborator_joined',\n  COLLABORATOR_LEFT: 'collaborator_left',\n  ITEM_RESERVED: 'item_reserved',\n  ITEM_PURCHASED: 'item_purchased',\n  ITEM_ADDED: 'item_added',\n  WISH_LIST_UPDATED: 'wish_list_updated'\n}\n```\n\n**Permission Filtering:**\n- Only send events to authorized collaborators\n- Filter sensitive data based on user role\n- Implement event payload sanitization\n- Add rate limiting per user\n\n**References:** Requirements.md - Real-time Events section", "linked_tasks": ["RT-001"], "epic": "Real-time Collaboration", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "RT-001-4", "summary": "Implement error handling and automatic reconnection", "description": "Add robust error handling and reconnection logic for real-time connections.\n\n**Acceptance Criteria:**\n- Implement exponential backoff for reconnection attempts\n- Handle network connectivity issues gracefully\n- Provide user feedback for connection problems\n- Queue events during disconnection periods\n- Implement connection timeout handling\n- Add retry limits to prevent infinite loops\n\n**Reconnection Strategy:**\n```javascript\nconst RECONNECTION_CONFIG = {\n  maxAttempts: 5,\n  baseDelay: 1000, // 1 second\n  maxDelay: 30000, // 30 seconds\n  backoffMultiplier: 2\n}\n```\n\n**Error Scenarios:**\n| Error Type | Handling Strategy | User Feedback |\n|------------|------------------|---------------|\n| Network Loss | Auto-reconnect with backoff | \"Reconnecting...\" |\n| Auth Failure | Redirect to login | \"Please log in again\" |\n| Permission Denied | Stop reconnection | \"Access denied\" |\n| Server Error | Retry with limits | \"Connection issues\" |\n\n**Implementation Details:**\n- Use existing error handling patterns\n- Integrate with notification system\n- Log connection issues for debugging\n- Provide manual reconnect option\n\n**References:** Requirements.md - Error Handling section", "linked_tasks": ["RT-001"], "epic": "Real-time Collaboration", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "RT-002", "summary": "Implement real-time event broadcasting", "description": "Create event broadcasting system for collaboration actions: gift_item_reserved, gift_item_purchased, gift_item_added, gift_item_updated, collaborator_joined, collaborator_left, wish_list_updated. Implement event payload standardization and ensure events are only sent to authorized collaborators. Include rate limiting and event deduplication.", "linked_tasks": ["RT-001"], "epic": "Real-time Collaboration", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "STORE-001", "summary": "Create gift lists Pinia store", "description": "Parent task for implementing gift lists state management. This task is broken down into sub-tasks: STORE-001-1 (Store structure and state), STORE-001-2 (CRUD actions), STORE-001-3 (Computed properties and filtering), and STORE-001-4 (Real-time integration).", "linked_tasks": ["API-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "STORE-001-1", "summary": "Set up gift lists store structure and state", "description": "Create the basic Pinia store structure for gift lists following existing patterns from friends.js and wishes.js.\n\n**Acceptance Criteria:**\n- Create `useGiftListsStore` with proper TypeScript types\n- Define state structure for gift lists, loading states, and errors\n- Implement store initialization and reset functionality\n- Follow existing naming conventions and patterns\n- Include proper state persistence if needed\n\n**Store Structure:**\n```javascript\nexport const useGiftListsStore = defineStore('giftLists', () => {\n  // State\n  const giftLists = ref([])\n  const loading = ref(false)\n  const error = ref(null)\n  const currentGiftList = ref(null)\n  const pagination = ref({ limit: 20, offset: 0, total: 0 })\n  \n  // Getters will be added in STORE-001-3\n  // Actions will be added in STORE-001-2\n  \n  return {\n    giftLists,\n    loading,\n    error,\n    currentGiftList,\n    pagination\n  }\n})\n```\n\n**TypeScript Interfaces:**\n- Use existing GiftList interface from Requirements.md\n- Add loading and error state types\n- Include pagination interface\n\n**References:** Existing friends.js and wishes.js store patterns", "linked_tasks": ["STORE-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "STORE-001-2", "summary": "Implement gift lists CRUD actions", "description": "Add all CRUD actions to the gift lists store with proper error handling and loading states.\n\n**Acceptance Criteria:**\n- Implement `fetchGiftLists()` with pagination support\n- Add `createGiftList(giftListData)` action\n- Create `updateGiftList(id, updates)` action\n- Implement `deleteGiftList(id)` action\n- Add `fetchGiftListById(id)` for single gift list\n- Include proper loading state management\n- Handle errors consistently across all actions\n\n**Action Implementation:**\n```javascript\nconst fetchGiftLists = async (options = {}) => {\n  loading.value = true\n  error.value = null\n  try {\n    const result = await supabaseService.getUserGiftLists(currentUser.id, options)\n    giftLists.value = result.data\n    pagination.value = result.pagination\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n```\n\n**Error Handling:**\n- Use consistent error message format\n- Clear errors on successful operations\n- Provide user-friendly error messages\n- Log errors for debugging\n\n**References:** Requirements.md - Gift List Management section", "linked_tasks": ["STORE-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "STORE-001-3", "summary": "Add computed properties and filtering for gift lists", "description": "Implement computed properties for filtering, sorting, and searching gift lists.\n\n**Acceptance Criteria:**\n- Add `myGiftLists` computed property (created by user)\n- Create `collaboratingLists` computed property (user is collaborator)\n- Implement `searchGiftLists(query)` function\n- Add filtering by status (active, completed, archived)\n- Create sorting options (by date, name, collaborator count)\n- Include statistics computations (total items, completion rate)\n\n**Computed Properties:**\n```javascript\nconst myGiftLists = computed(() => \n  giftLists.value.filter(list => list.createdBy === currentUser.id)\n)\n\nconst collaboratingLists = computed(() => \n  giftLists.value.filter(list => \n    list.createdBy !== currentUser.id && \n    list.collaborators?.some(c => c.userId === currentUser.id)\n  )\n)\n\nconst searchGiftLists = (query) => computed(() => \n  giftLists.value.filter(list => \n    list.name.toLowerCase().includes(query.toLowerCase()) ||\n    list.birthdayPerson.name.toLowerCase().includes(query.toLowerCase())\n  )\n)\n```\n\n**Filtering Options:**\n| Filter | Description | Implementation |\n|--------|-------------|----------------|\n| Status | Active/Completed/Archived | Filter by is_active and completion |\n| Role | Creator/Collaborator | Filter by user relationship |\n| Date | Recent/Upcoming | Sort by created_at or birthday date |\n\n**References:** Requirements.md - Gift Lists Overview Screen", "linked_tasks": ["STORE-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "STORE-001-4", "summary": "Integrate real-time updates with gift lists store", "description": "Add real-time subscription management and automatic state updates for gift list changes.\n\n**Acceptance Criteria:**\n- Subscribe to real-time updates for user's gift lists\n- Handle incoming real-time events and update state\n- Implement optimistic updates for user actions\n- Add conflict resolution for concurrent changes\n- Manage subscription lifecycle (subscribe/unsubscribe)\n- Update UI reactively when changes occur\n\n**Real-time Integration:**\n```javascript\nconst subscribeToGiftListUpdates = () => {\n  // Subscribe to changes in user's gift lists\n  const subscription = supabase\n    .channel('user-gift-lists')\n    .on('postgres_changes', {\n      event: '*',\n      schema: 'public',\n      table: 'gift_lists',\n      filter: `created_by=eq.${currentUser.id}`\n    }, handleGiftListChange)\n    .subscribe()\n    \n  return subscription\n}\n```\n\n**Event Handling:**\n- INSERT: Add new gift list to state\n- UPDATE: Update existing gift list in state\n- DELETE: Remove gift list from state\n- Handle events from other users (collaborator changes)\n\n**Optimistic Updates:**\n- Update UI immediately for user actions\n- Revert changes if server operation fails\n- Show loading indicators during sync\n\n**References:** Requirements.md - Real-time Collaboration section", "linked_tasks": ["STORE-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "STORE-002", "summary": "Create collaborators Pinia store", "description": "Parent task for implementing collaborators state management. This task is broken down into sub-tasks: STORE-002-1 (Store structure), STORE-002-2 (Collaborator actions), STORE-002-3 (Permission management), and STORE-002-4 (Real-time collaboration state).", "linked_tasks": ["API-002", "STORE-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "STORE-002-1", "summary": "Set up collaborators store structure and state", "description": "Create the basic Pinia store structure for collaborator management.\n\n**Acceptance Criteria:**\n- Create `useCollaboratorsStore` with proper TypeScript types\n- Define state for collaborators, invitations, and permissions\n- Implement store initialization and cleanup\n- Follow existing store patterns and conventions\n- Include proper state organization by gift list\n\n**Store Structure:**\n```javascript\nexport const useCollaboratorsStore = defineStore('collaborators', () => {\n  // State organized by gift list ID\n  const collaboratorsByGiftList = ref(new Map())\n  const pendingInvitations = ref([])\n  const loading = ref(false)\n  const error = ref(null)\n  const currentUserPermissions = ref(new Map())\n  \n  return {\n    collaboratorsByGiftList,\n    pendingInvitations,\n    loading,\n    error,\n    currentUserPermissions\n  }\n})\n```\n\n**TypeScript Interfaces:**\n```typescript\ninterface CollaboratorState {\n  id: string\n  userId: string\n  giftListId: string\n  role: 'ADMIN' | 'COLLABORATOR'\n  status: 'PENDING' | 'ACCEPTED' | 'DECLINED'\n  invitedAt: Date\n  acceptedAt?: Date\n  user: UserProfile\n}\n```\n\n**References:** Existing store patterns in friends.js", "linked_tasks": ["STORE-002"], "epic": "State Management", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "STORE-002-2", "summary": "Implement collaborator management actions", "description": "Add actions for managing collaborators: invitations, role changes, and removal.\n\n**Acceptance Criteria:**\n- Implement `fetchCollaborators(giftListId)` action\n- Add `inviteCollaborator(giftListId, userId, role)` action\n- Create `updateCollaboratorRole(collaboratorId, newRole)` action\n- Implement `removeCollaborator(collaboratorId)` action\n- Add `acceptInvitation(collaboratorId)` and `declineInvitation(collaboratorId)`\n- Include proper error handling and loading states\n\n**Action Implementation:**\n```javascript\nconst inviteCollaborator = async (giftListId, userId, role) => {\n  loading.value = true\n  error.value = null\n  try {\n    const invitation = await supabaseService.inviteCollaborator(giftListId, userId, role)\n    \n    // Add to pending invitations\n    pendingInvitations.value.push(invitation)\n    \n    // Update collaborators list\n    const collaborators = collaboratorsByGiftList.value.get(giftListId) || []\n    collaborators.push(invitation)\n    collaboratorsByGiftList.value.set(giftListId, collaborators)\n    \n    return invitation\n  } catch (err) {\n    error.value = err.message\n    throw err\n  } finally {\n    loading.value = false\n  }\n}\n```\n\n**State Updates:**\n- Update local state optimistically\n- Revert on API errors\n- Sync with real-time updates\n\n**References:** Requirements.md - Collaborator Management section", "linked_tasks": ["STORE-002"], "epic": "State Management", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "STORE-002-3", "summary": "Implement permission management and role checking", "description": "Add computed properties and functions for checking user permissions and roles.\n\n**Acceptance Criteria:**\n- Create `getUserRole(giftListId)` computed property\n- Implement `canManageCollaborators(giftListId)` permission check\n- Add `canEditGiftList(giftListId)` permission check\n- Create `isAdmin(giftListId)` helper function\n- Implement role-based filtering for UI components\n- Add permission caching for performance\n\n**Permission Helpers:**\n```javascript\nconst getUserRole = (giftListId) => computed(() => {\n  const collaborators = collaboratorsByGiftList.value.get(giftListId) || []\n  const userCollaboration = collaborators.find(c => c.userId === currentUser.id)\n  return userCollaboration?.role || null\n})\n\nconst canManageCollaborators = (giftListId) => computed(() => {\n  const role = getUserRole(giftListId).value\n  return role === 'ADMIN'\n})\n\nconst canEditGiftList = (giftListId) => computed(() => {\n  const role = getUserRole(giftListId).value\n  return ['ADMIN', 'COLLABORATOR'].includes(role)\n})\n```\n\n**Permission Matrix:**\n| Action | Admin | Collaborator | Non-member |\n|--------|-------|--------------|------------|\n| View Gift List | ✓ | ✓ | ✗ |\n| Add Items | ✓ | ✓ | ✗ |\n| Reserve Items | ✓ | ✓ | ✗ |\n| Invite Collaborators | ✓ | ✗ | ✗ |\n| Remove Collaborators | ✓ | ✗ | ✗ |\n| Delete Gift List | ✓ | ✗ | ✗ |\n\n**References:** Requirements.md - Security & Permissions section", "linked_tasks": ["STORE-002"], "epic": "State Management", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "STORE-002-4", "summary": "Integrate real-time collaboration state updates", "description": "Add real-time subscription management for collaborator changes and live collaboration features.\n\n**Acceptance Criteria:**\n- Subscribe to collaborator changes for active gift lists\n- Handle real-time invitation status updates\n- Implement presence indicators for active collaborators\n- Add real-time role change notifications\n- Update UI reactively for collaboration events\n- Manage subscription cleanup on component unmount\n\n**Real-time Subscriptions:**\n```javascript\nconst subscribeToCollaboratorUpdates = (giftListId) => {\n  const subscription = supabase\n    .channel(`gift-list-collaborators:${giftListId}`)\n    .on('postgres_changes', {\n      event: '*',\n      schema: 'public',\n      table: 'gift_list_collaborators',\n      filter: `gift_list_id=eq.${giftListId}`\n    }, handleCollaboratorChange)\n    .subscribe()\n    \n  return subscription\n}\n```\n\n**Event Handling:**\n- INSERT: New collaborator invited/joined\n- UPDATE: Role changed or invitation accepted\n- DELETE: Collaborator removed from gift list\n\n**Presence Management:**\n- Track active collaborators in real-time\n- Show online/offline status indicators\n- Display \"typing\" or \"editing\" indicators\n- Handle presence cleanup on disconnect\n\n**Live Collaboration Features:**\n- Real-time cursor positions (future enhancement)\n- Live editing indicators\n- Conflict resolution for simultaneous edits\n- Activity feed updates\n\n**References:** Requirements.md - Real-time Collaboration section", "linked_tasks": ["STORE-002"], "epic": "State Management", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "STORE-003", "summary": "Extend wishes store for collaboration features", "description": "Extend existing useWishesStore to support collaboration features. Add new actions: reserveWish(), unreserveWish(), markAsPurchased(), addToGiftList(). Update existing computed properties to handle collaboration status filtering. Add real-time subscription management for gift list wish updates. Ensure backward compatibility with existing wish management functionality.", "linked_tasks": ["API-004", "STORE-001"], "epic": "State Management", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "STORE-004", "summary": "Create wish list items Pinia store", "description": "Create useWishListItemsStore for managing birthday person's personal wish lists. Include state for: wishListItems array, privacy settings, sharing permissions. Implement actions: fetchWishListItems(), createWishListItem(), updateWishListItem(), deleteWishListItem(), updatePrivacySettings(), shareWithCollaborators(). Include computed properties for public/private filtering and integration with gift list collaboration.", "linked_tasks": ["API-003"], "epic": "State Management", "priority": "Medium", "estimated_effort": "Medium", "type": "Story"}, {"id": "COMP-001", "summary": "Create GiftListCard component", "description": "Create reusable GiftListCard.vue component following existing card patterns (BirthdayCard.vue, FriendCard.vue). Display: gift list name, birthday person info, collaborator count, item count, completion status, user's role. Include action buttons: view details, manage collaborators (admin only), leave collaboration. Support real-time updates for collaborator and item counts. Use Vuetify components consistent with existing design system.", "linked_tasks": ["STORE-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "COMP-002", "summary": "Create CollaboratorCard component", "description": "Create CollaboratorCard.vue component to display collaborator information. Show: user avatar, name, role badge, status indicator, join date. Include action buttons for admins: change role, remove collaborator. Support different states: pending invitation, active collaborator, declined invitation. Include real-time presence indicators and follow existing card design patterns.", "linked_tasks": ["STORE-002"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Small", "type": "Story"}, {"id": "COMP-003", "summary": "Create GiftListItemCard component", "description": "Create GiftListItemCard.vue component extending existing WishCard.vue patterns. Display: item details, price, priority, status (Available/Reserved/Purchased), reserved/purchased by info. Include action buttons: reserve/unreserve, mark as purchased, edit (if creator), delete (if creator/admin). Support real-time status updates and optimistic UI updates. Include conflict resolution UI for concurrent actions.", "linked_tasks": ["STORE-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "COMP-004", "summary": "Create InviteCollaboratorsDialog component", "description": "Parent task for implementing collaborator invitation dialog. This task is broken down into sub-tasks: COMP-004-1 (Dialog structure), COMP-004-2 (Friend selection), COMP-004-3 (Invitation form), and COMP-004-4 (Bulk operations).", "linked_tasks": ["STORE-002", "COMP-002"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Small", "type": "Story"}, {"id": "COMP-004-1", "summary": "Create dialog structure and layout for InviteCollaboratorsDialog", "description": "Set up the basic modal dialog structure using Vuetify components following existing dialog patterns.\n\n**Acceptance Criteria:**\n- Create modal dialog using v-dialog component\n- Implement responsive design for mobile and desktop\n- Add proper dialog header with title and close button\n- Include dialog footer with action buttons\n- Follow existing dialog patterns from the codebase\n- Add proper accessibility attributes (ARIA labels)\n\n**Component Structure:**\n```vue\n<template>\n  <v-dialog\n    v-model=\"isOpen\"\n    max-width=\"600px\"\n    persistent\n    scrollable\n  >\n    <v-card>\n      <v-card-title>\n        <span class=\"text-h5\">Invite Collaborators</span>\n        <v-spacer></v-spacer>\n        <v-btn icon @click=\"close\">\n          <v-icon>mdi-close</v-icon>\n        </v-btn>\n      </v-card-title>\n      \n      <v-card-text>\n        <!-- Content will be added in other sub-tasks -->\n      </v-card-text>\n      \n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn text @click=\"close\">Cancel</v-btn>\n        <v-btn color=\"primary\" @click=\"sendInvitations\">Send Invitations</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n```\n\n**Props and Events:**\n- `modelValue` (Boolean): Dialog open/close state\n- `giftListId` (String): ID of gift list for invitations\n- `@update:modelValue`: Emit dialog state changes\n- `@invitations-sent`: Emit when invitations are successfully sent\n\n**References:** Existing dialog components in codebase", "linked_tasks": ["COMP-004"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Small", "type": "Task"}, {"id": "COMP-004-2", "summary": "Implement friend selection with search functionality", "description": "Add searchable friend selection interface with filtering and multi-select capabilities.\n\n**Acceptance Criteria:**\n- Implement searchable friend list using v-autocomplete\n- Filter out users already collaborating on the gift list\n- Support multi-select for bulk invitations\n- Display friend avatars and names in selection\n- Add \"Select All\" and \"Clear All\" options\n- Include search by name and email functionality\n\n**Friend Selection Interface:**\n```vue\n<v-autocomplete\n  v-model=\"selectedFriends\"\n  :items=\"availableFriends\"\n  :search=\"searchQuery\"\n  item-title=\"name\"\n  item-value=\"id\"\n  multiple\n  chips\n  closable-chips\n  label=\"Search and select friends\"\n  placeholder=\"Type to search friends...\"\n>\n  <template #item=\"{ props, item }\">\n    <v-list-item v-bind=\"props\">\n      <template #prepend>\n        <v-avatar size=\"32\">\n          <v-img :src=\"item.avatar_url\" :alt=\"item.name\" />\n        </v-avatar>\n      </template>\n      <v-list-item-title>{{ item.name }}</v-list-item-title>\n      <v-list-item-subtitle>{{ item.email }}</v-list-item-subtitle>\n    </v-list-item>\n  </template>\n</v-autocomplete>\n```\n\n**Filtering Logic:**\n- Exclude current collaborators\n- Exclude users with pending invitations\n- Support search by name, email, or username\n- Sort results by relevance and recent interaction\n\n**Bulk Selection:**\n- \"Select All Visible\" button\n- \"Clear Selection\" button\n- Show selected count indicator\n\n**References:** Requirements.md - Collaborator Invitation Screen", "linked_tasks": ["COMP-004"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Medium", "type": "Task"}, {"id": "COMP-004-3", "summary": "Create invitation form with role assignment and custom message", "description": "Implement the invitation form with role selection and personalized message options.\n\n**Acceptance Criteria:**\n- Add role selection (Admin/Collaborator) for each invitee\n- Include custom invitation message text area\n- Implement form validation for required fields\n- Add preview of invitation email content\n- Support different roles for different invitees\n- Include invitation message templates\n\n**Form Structure:**\n```vue\n<v-form ref=\"invitationForm\" v-model=\"formValid\">\n  <!-- Role Assignment Section -->\n  <v-card-subtitle>Role Assignment</v-card-subtitle>\n  <v-radio-group v-model=\"defaultRole\" row>\n    <v-radio label=\"Collaborator\" value=\"COLLABORATOR\"></v-radio>\n    <v-radio label=\"Admin\" value=\"ADMIN\"></v-radio>\n  </v-radio-group>\n  \n  <!-- Individual Role Override -->\n  <v-expansion-panels v-if=\"selectedFriends.length > 1\">\n    <v-expansion-panel title=\"Customize individual roles\">\n      <v-expansion-panel-text>\n        <div v-for=\"friend in selectedFriends\" :key=\"friend.id\">\n          <v-select\n            v-model=\"individualRoles[friend.id]\"\n            :items=\"roleOptions\"\n            :label=\"`Role for ${friend.name}`\"\n          ></v-select>\n        </div>\n      </v-expansion-panel-text>\n    </v-expansion-panel>\n  </v-expansion-panels>\n  \n  <!-- Custom Message -->\n  <v-textarea\n    v-model=\"customMessage\"\n    label=\"Custom invitation message (optional)\"\n    placeholder=\"Add a personal message to your invitation...\"\n    rows=\"3\"\n    counter=\"500\"\n    :rules=\"messageRules\"\n  ></v-textarea>\n</v-form>\n```\n\n**Validation Rules:**\n- At least one friend must be selected\n- Custom message length limit (500 characters)\n- Role selection validation\n- Email format validation for external invites\n\n**Message Templates:**\n- Default invitation message\n- Birthday-specific templates\n- Relationship-based templates (family, friends, colleagues)\n\n**References:** Requirements.md - Invitation system requirements", "linked_tasks": ["COMP-004"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Medium", "type": "Task"}, {"id": "COMP-004-4", "summary": "Implement bulk operations and invitation sending", "description": "Add bulk invitation processing, loading states, and success/error feedback.\n\n**Acceptance Criteria:**\n- Process multiple invitations simultaneously\n- Show progress indicator for bulk operations\n- Handle partial success scenarios (some invitations fail)\n- Display detailed success/error feedback\n- Implement retry mechanism for failed invitations\n- Add confirmation dialog for bulk operations\n\n**Bulk Processing Logic:**\n```javascript\nconst sendInvitations = async () => {\n  loading.value = true\n  const results = {\n    successful: [],\n    failed: [],\n    total: selectedFriends.value.length\n  }\n  \n  for (const friend of selectedFriends.value) {\n    try {\n      const role = individualRoles.value[friend.id] || defaultRole.value\n      const invitation = await collaboratorsStore.inviteCollaborator(\n        props.giftListId,\n        friend.id,\n        role,\n        customMessage.value\n      )\n      results.successful.push({ friend, invitation })\n    } catch (error) {\n      results.failed.push({ friend, error: error.message })\n    }\n  }\n  \n  showResults(results)\n  loading.value = false\n}\n```\n\n**Progress Feedback:**\n- Progress bar showing invitation sending progress\n- Individual invitation status indicators\n- Real-time success/failure updates\n- Final summary with retry options\n\n**Error Handling:**\n| Error Type | User Feedback | Action |\n|------------|---------------|--------|\n| Network Error | \"Connection issue, please retry\" | Retry button |\n| Permission Denied | \"Cannot invite this user\" | Remove from selection |\n| User Not Found | \"User not found\" | Remove from selection |\n| Already Invited | \"User already invited\" | Skip invitation |\n\n**Success Feedback:**\n- Toast notification for successful invitations\n- Updated collaborator list in parent component\n- Email confirmation status\n- Option to send additional invitations\n\n**References:** Requirements.md - Collaboration workflows", "linked_tasks": ["COMP-004"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Medium", "type": "Task"}, {"id": "COMP-005", "summary": "Create ActivityFeed component", "description": "Create ActivityFeed.vue component to display real-time collaboration activity. Show: recent actions (item reserved, purchased, collaborator joined), timestamps, user avatars, action descriptions. Include filtering options (by user, by action type), pagination for history, and real-time updates. Follow existing list patterns and include proper loading states.", "linked_tasks": ["RT-002"], "epic": "Gift List UI", "priority": "Low", "estimated_effort": "Medium", "type": "Story"}, {"id": "COMP-006", "summary": "Enhance <PERSON> with collaboration features", "description": "Extend existing BirthdayCard.vue component to include collaboration indicators: active gift list badge, collaborator count, user participation status (creator/collaborator/not participating). Add action buttons: Create Gift List, Join Gift List, View Gift List. Maintain existing functionality and design patterns while adding new collaboration features. Include real-time updates for collaboration status.", "linked_tasks": ["STORE-001", "COMP-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "VIEW-001", "summary": "Create GiftListsOverviewView", "description": "Parent task for implementing gift lists overview page. This task is broken down into sub-tasks: VIEW-001-1 (Page structure and layout), VIEW-001-2 (Gift list sections), VIEW-001-3 (Search and filtering), and VIEW-001-4 (Real-time updates and actions).", "linked_tasks": ["STORE-001", "STORE-002", "COMP-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "VIEW-001-1", "summary": "Create page structure and layout for GiftListsOverviewView", "description": "Set up the basic page structure and responsive layout following existing view patterns.\n\n**Acceptance Criteria:**\n- Create Vue component following FriendsView.vue patterns\n- Implement responsive layout for mobile and desktop\n- Add page header with title and primary actions\n- Include proper loading states and error handling\n- Set up navigation and breadcrumbs\n- Add empty state handling\n\n**Page Structure:**\n```vue\n<template>\n  <div class=\"gift-lists-overview\">\n    <!-- Page Header -->\n    <div class=\"page-header\">\n      <h1 class=\"text-h4\">Gift Lists</h1>\n      <v-spacer></v-spacer>\n      <v-btn color=\"primary\" @click=\"createGiftList\">\n        <v-icon left>mdi-plus</v-icon>\n        Create Gift List\n      </v-btn>\n    </div>\n    \n    <!-- Search and Filters -->\n    <div class=\"filters-section\">\n      <!-- Will be implemented in VIEW-001-3 -->\n    </div>\n    \n    <!-- Content Sections -->\n    <div class=\"content-sections\">\n      <!-- Will be implemented in VIEW-001-2 -->\n    </div>\n  </div>\n</template>\n```\n\n**Responsive Design:**\n- Mobile: Single column layout with collapsible sections\n- Tablet: Two-column layout for better space utilization\n- Desktop: Three-column layout with sidebar filters\n\n**Loading States:**\n- Skeleton loaders for gift list cards\n- Progressive loading for different sections\n- Shimmer effects during data fetching\n\n**Error Handling:**\n- Network error messages with retry options\n- Empty state illustrations and guidance\n- Permission error handling\n\n**References:** Existing FriendsView.vue and BirthdaysView.vue patterns", "linked_tasks": ["VIEW-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "VIEW-001-2", "summary": "Implement gift list sections (My Lists, Collaborating, Invitations)", "description": "Create the three main sections for organizing different types of gift list relationships.\n\n**Acceptance Criteria:**\n- Implement \"My Gift Lists\" section showing user-created lists\n- Add \"Collaborating Lists\" section for lists user is invited to\n- Create \"Pending Invitations\" section for outstanding invites\n- Include section headers with counts and quick actions\n- Add expand/collapse functionality for each section\n- Implement infinite scrolling or pagination within sections\n\n**Section Implementation:**\n```vue\n<template>\n  <div class=\"gift-list-sections\">\n    <!-- My Gift Lists -->\n    <v-expansion-panels v-model=\"expandedSections\" multiple>\n      <v-expansion-panel value=\"my-lists\">\n        <v-expansion-panel-title>\n          <div class=\"section-header\">\n            <h3>My Gift Lists</h3>\n            <v-chip size=\"small\">{{ myGiftLists.length }}</v-chip>\n          </div>\n        </v-expansion-panel-title>\n        <v-expansion-panel-text>\n          <div class=\"gift-list-grid\">\n            <GiftListCard\n              v-for=\"giftList in myGiftLists\"\n              :key=\"giftList.id\"\n              :gift-list=\"giftList\"\n              :user-role=\"'ADMIN'\"\n              @click=\"viewGiftList(giftList.id)\"\n            />\n          </div>\n        </v-expansion-panel-text>\n      </v-expansion-panel>\n      \n      <!-- Similar structure for other sections -->\n    </v-expansion-panels>\n  </div>\n</template>\n```\n\n**Section Features:**\n| Section | Features | Actions |\n|---------|----------|----------|\n| My Gift Lists | Created by user, admin controls | Edit, Delete, Duplicate, Archive |\n| Collaborating Lists | User is collaborator | View, Leave Collaboration |\n| Pending Invitations | Outstanding invites | Accept, Decline, View Details |\n\n**Quick Actions:**\n- Bulk selection for multiple operations\n- Sort options (by date, name, activity)\n- Filter options (active, completed, archived)\n- Export options for completed lists\n\n**Empty States:**\n- Encouraging messages for each section\n- Call-to-action buttons for creating first gift list\n- Helpful tips for collaboration features\n\n**References:** Requirements.md - Gift Lists Overview Screen", "linked_tasks": ["VIEW-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-001-3", "summary": "Implement search and filtering functionality", "description": "Add comprehensive search and filtering capabilities across all gift list sections.\n\n**Acceptance Criteria:**\n- Implement global search across all gift lists\n- Add filtering by status (active, completed, archived)\n- Include filtering by role (creator, collaborator)\n- Add date range filtering (created, birthday date)\n- Implement sorting options (name, date, activity)\n- Include saved filter presets\n\n**Search Implementation:**\n```vue\n<template>\n  <div class=\"search-filters\">\n    <v-text-field\n      v-model=\"searchQuery\"\n      label=\"Search gift lists\"\n      prepend-inner-icon=\"mdi-magnify\"\n      clearable\n      @input=\"handleSearch\"\n    ></v-text-field>\n    \n    <v-row class=\"filter-row\">\n      <v-col cols=\"12\" md=\"3\">\n        <v-select\n          v-model=\"statusFilter\"\n          :items=\"statusOptions\"\n          label=\"Status\"\n          clearable\n        ></v-select>\n      </v-col>\n      \n      <v-col cols=\"12\" md=\"3\">\n        <v-select\n          v-model=\"roleFilter\"\n          :items=\"roleOptions\"\n          label=\"My Role\"\n          clearable\n        ></v-select>\n      </v-col>\n      \n      <v-col cols=\"12\" md=\"3\">\n        <v-select\n          v-model=\"sortBy\"\n          :items=\"sortOptions\"\n          label=\"Sort By\"\n        ></v-select>\n      </v-col>\n      \n      <v-col cols=\"12\" md=\"3\">\n        <v-btn-toggle v-model=\"sortOrder\" mandatory>\n          <v-btn value=\"asc\"><v-icon>mdi-sort-ascending</v-icon></v-btn>\n          <v-btn value=\"desc\"><v-icon>mdi-sort-descending</v-icon></v-btn>\n        </v-btn-toggle>\n      </v-col>\n    </v-row>\n  </div>\n</template>\n```\n\n**Filter Options:**\n- **Status**: Active, Completed, Archived, All\n- **Role**: Creator, Admin, Collaborator, All\n- **Date Range**: Last week, Last month, Custom range\n- **Activity**: Recently active, No recent activity\n\n**Search Functionality:**\n- Search by gift list name\n- Search by birthday person name\n- Search by collaborator names\n- Search by gift items (future enhancement)\n\n**Saved Filters:**\n- \"My Active Lists\"\n- \"Recent Collaborations\"\n- \"Pending Actions\"\n- Custom user-defined filters\n\n**Performance Optimization:**\n- Debounced search input\n- Client-side filtering for small datasets\n- Server-side filtering for large datasets\n- Cached filter results\n\n**References:** Requirements.md - Search and filtering requirements", "linked_tasks": ["VIEW-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-001-4", "summary": "Add real-time updates and quick actions", "description": "Implement real-time updates for gift list changes and quick action functionality.\n\n**Acceptance Criteria:**\n- Subscribe to real-time updates for all user's gift lists\n- Update UI automatically when gift lists change\n- Add quick actions menu for each gift list\n- Implement bulk operations for multiple selections\n- Add confirmation dialogs for destructive actions\n- Include undo functionality for reversible actions\n\n**Real-time Updates:**\n```javascript\n// Subscribe to changes in user's gift lists\nconst subscribeToUpdates = () => {\n  // Subscribe to gift lists the user created\n  giftListsStore.subscribeToUserGiftLists(currentUser.id)\n  \n  // Subscribe to collaboration changes\n  collaboratorsStore.subscribeToUserCollaborations(currentUser.id)\n  \n  // Subscribe to invitation updates\n  collaboratorsStore.subscribeToPendingInvitations(currentUser.id)\n}\n```\n\n**Quick Actions:**\n| Action | Availability | Confirmation Required |\n|--------|--------------|----------------------|\n| View Details | All lists | No |\n| Edit Gift List | Admin only | No |\n| Duplicate List | All lists | No |\n| Archive List | Admin only | Yes |\n| Delete List | Admin only | Yes |\n| Leave Collaboration | Collaborator only | Yes |\n| Accept Invitation | Pending only | No |\n| Decline Invitation | Pending only | Yes |\n\n**Bulk Operations:**\n- Select multiple gift lists with checkboxes\n- Bulk archive for completed lists\n- Bulk delete for admin-owned lists\n- Bulk export for data backup\n\n**Real-time Indicators:**\n- Live activity indicators on gift list cards\n- New invitation badges\n- Recent activity timestamps\n- Online collaborator presence\n\n**Undo Functionality:**\n- Undo archive action (30-second window)\n- Undo leave collaboration (5-minute window)\n- Restore from trash for deleted lists\n\n**References:** Requirements.md - Real-time collaboration features", "linked_tasks": ["VIEW-001"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-002", "summary": "Create CreateGiftListView", "description": "Create new view (/gift-lists/create) for gift list creation. Include: birthday person selection, gift list details form, initial collaborator invitations, preferences setup. Follow existing form patterns (AddFriendView.vue, AddWishView.vue) with proper validation, loading states, and error handling. Include integration options: import from wish list, copy from previous gift lists, template suggestions.", "linked_tasks": ["STORE-001", "COMP-004"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Large", "type": "Story"}, {"id": "VIEW-003", "summary": "Create GiftListDetailView", "description": "Parent task for implementing detailed gift list management page. This task is broken down into sub-tasks: VIEW-003-1 (Page layout and header), VIEW-003-2 (Gift list items management), VIEW-003-3 (Collaborator management section), VIEW-003-4 (Wish list integration), and VIEW-003-5 (Real-time features and activity feed).", "linked_tasks": ["STORE-001", "STORE-002", "COMP-003", "COMP-005"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "VIEW-003-1", "summary": "Create page layout and header for GiftListDetailView", "description": "Set up the basic page structure, header, and navigation for the gift list detail page.\n\n**Acceptance Criteria:**\n- Create responsive page layout following existing detail view patterns\n- Implement gift list header with key information and actions\n- Add breadcrumb navigation and back button\n- Include role-based action buttons in header\n- Add loading states and error handling\n- Implement proper page title and meta information\n\n**Page Header Structure:**\n```vue\n<template>\n  <div class=\"gift-list-detail\">\n    <!-- Breadcrumb Navigation -->\n    <v-breadcrumbs :items=\"breadcrumbItems\" class=\"pa-0\">\n      <template #item=\"{ item }\">\n        <v-breadcrumbs-item\n          :to=\"item.to\"\n          :disabled=\"item.disabled\"\n        >\n          {{ item.title }}\n        </v-breadcrumbs-item>\n      </template>\n    </v-breadcrumbs>\n    \n    <!-- Gift List Header -->\n    <div class=\"gift-list-header\">\n      <div class=\"header-content\">\n        <div class=\"gift-list-info\">\n          <h1 class=\"text-h4\">{{ giftList.name }}</h1>\n          <div class=\"birthday-info\">\n            <v-avatar size=\"40\" class=\"mr-3\">\n              <v-img :src=\"birthdayPerson.avatar_url\" />\n            </v-avatar>\n            <div>\n              <div class=\"text-h6\">{{ birthdayPerson.name }}'s Birthday</div>\n              <div class=\"text-body-2 text-medium-emphasis\">\n                {{ formatDate(birthdayPerson.birthday) }} • {{ daysUntilBirthday }} days\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"header-actions\">\n          <!-- Role-based actions will be added -->\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n**Role-based Actions:**\n| Role | Available Actions |\n|------|------------------|\n| Admin | Edit, Invite Collaborators, Archive, Delete |\n| Collaborator | Add Items, Export List |\n| Viewer | Export List (read-only) |\n\n**Loading States:**\n- Skeleton loader for header information\n- Progressive loading of different sections\n- Shimmer effects for dynamic content\n\n**Error Handling:**\n- Gift list not found (404)\n- Permission denied (403)\n- Network errors with retry options\n\n**References:** Existing detail view patterns in codebase", "linked_tasks": ["VIEW-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "VIEW-003-2", "summary": "Implement gift list items management section", "description": "Create the main section for displaying and managing gift list items with real-time updates.\n\n**Acceptance Criteria:**\n- Display gift list items using GiftListItemCard components\n- Implement add new item functionality\n- Add filtering and sorting options for items\n- Include item status indicators (Available/Reserved/Purchased)\n- Support real-time updates for item changes\n- Add bulk operations for multiple items\n\n**Items Section Layout:**\n```vue\n<template>\n  <div class=\"gift-list-items-section\">\n    <!-- Section Header -->\n    <div class=\"section-header\">\n      <h2 class=\"text-h5\">Gift Items</h2>\n      <div class=\"items-stats\">\n        <v-chip size=\"small\" color=\"success\">\n          {{ purchasedCount }} Purchased\n        </v-chip>\n        <v-chip size=\"small\" color=\"warning\">\n          {{ reservedCount }} Reserved\n        </v-chip>\n        <v-chip size=\"small\">\n          {{ availableCount }} Available\n        </v-chip>\n      </div>\n      <v-spacer></v-spacer>\n      <v-btn\n        v-if=\"canAddItems\"\n        color=\"primary\"\n        @click=\"showAddItemDialog\"\n      >\n        <v-icon left>mdi-plus</v-icon>\n        Add Item\n      </v-btn>\n    </div>\n    \n    <!-- Filters and Search -->\n    <div class=\"items-filters\">\n      <v-text-field\n        v-model=\"itemSearch\"\n        label=\"Search items\"\n        prepend-inner-icon=\"mdi-magnify\"\n        clearable\n      ></v-text-field>\n      \n      <v-select\n        v-model=\"statusFilter\"\n        :items=\"statusFilterOptions\"\n        label=\"Filter by status\"\n        clearable\n      ></v-select>\n      \n      <v-select\n        v-model=\"sortBy\"\n        :items=\"sortOptions\"\n        label=\"Sort by\"\n      ></v-select>\n    </div>\n    \n    <!-- Items Grid -->\n    <div class=\"items-grid\">\n      <GiftListItemCard\n        v-for=\"item in filteredItems\"\n        :key=\"item.id\"\n        :item=\"item\"\n        :user-role=\"userRole\"\n        :can-edit=\"canEditItem(item)\"\n        @reserve=\"handleReserveItem\"\n        @purchase=\"handlePurchaseItem\"\n        @edit=\"handleEditItem\"\n        @delete=\"handleDeleteItem\"\n      />\n    </div>\n  </div>\n</template>\n```\n\n**Item Management Features:**\n- Add new items with details (name, price, link, priority)\n- Edit existing items (creator and admin only)\n- Reserve/unreserve items\n- Mark items as purchased\n- Delete items (creator and admin only)\n\n**Real-time Updates:**\n- Live status changes when collaborators take actions\n- Optimistic UI updates with rollback on failure\n- Real-time notifications for item changes\n- Conflict resolution for simultaneous actions\n\n**Filtering and Sorting:**\n- Filter by status (All, Available, Reserved, Purchased)\n- Filter by price range\n- Sort by name, price, priority, date added\n- Search by item name and description\n\n**References:** Requirements.md - Gift List Management Screen", "linked_tasks": ["VIEW-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-003-3", "summary": "Create collaborator management section", "description": "Implement the collaborator management interface with role-based permissions.\n\n**Acceptance Criteria:**\n- Display current collaborators with roles and status\n- Add invite new collaborators functionality (admin only)\n- Implement role change capabilities (admin only)\n- Add remove collaborator functionality (admin only)\n- Show pending invitations with management options\n- Include collaborator activity indicators\n\n**Collaborator Section:**\n```vue\n<template>\n  <div class=\"collaborators-section\">\n    <div class=\"section-header\">\n      <h3 class=\"text-h6\">Collaborators</h3>\n      <v-chip size=\"small\">{{ collaborators.length }}</v-chip>\n      <v-spacer></v-spacer>\n      <v-btn\n        v-if=\"isAdmin\"\n        size=\"small\"\n        @click=\"showInviteDialog\"\n      >\n        <v-icon left>mdi-account-plus</v-icon>\n        Invite\n      </v-btn>\n    </div>\n    \n    <!-- Active Collaborators -->\n    <div class=\"collaborators-list\">\n      <CollaboratorCard\n        v-for=\"collaborator in activeCollaborators\"\n        :key=\"collaborator.id\"\n        :collaborator=\"collaborator\"\n        :current-user-role=\"userRole\"\n        :can-manage=\"isAdmin\"\n        @change-role=\"handleRoleChange\"\n        @remove=\"handleRemoveCollaborator\"\n      />\n    </div>\n    \n    <!-- Pending Invitations -->\n    <div v-if=\"pendingInvitations.length > 0\" class=\"pending-invitations\">\n      <h4 class=\"text-subtitle-1\">Pending Invitations</h4>\n      <div class=\"invitations-list\">\n        <div\n          v-for=\"invitation in pendingInvitations\"\n          :key=\"invitation.id\"\n          class=\"invitation-item\"\n        >\n          <div class=\"invitation-info\">\n            <span>{{ invitation.user.name }}</span>\n            <v-chip size=\"x-small\" color=\"orange\">{{ invitation.role }}</v-chip>\n          </div>\n          <div class=\"invitation-actions\">\n            <v-btn\n              v-if=\"isAdmin\"\n              size=\"x-small\"\n              icon=\"mdi-close\"\n              @click=\"cancelInvitation(invitation.id)\"\n            ></v-btn>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n**Permission Matrix:**\n| Action | Admin | Collaborator | Pending |\n|--------|-------|--------------|----------|\n| View Collaborators | ✓ | ✓ | ✗ |\n| Invite New | ✓ | ✗ | ✗ |\n| Change Roles | ✓ | ✗ | ✗ |\n| Remove Collaborators | ✓ | ✗ | ✗ |\n| Cancel Invitations | ✓ | ✗ | ✗ |\n\n**Collaborator Features:**\n- Real-time presence indicators\n- Activity status (online, last seen)\n- Role badges and permissions\n- Quick actions menu\n- Bulk selection for admin operations\n\n**Invitation Management:**\n- Send new invitations with custom messages\n- Resend pending invitations\n- Cancel pending invitations\n- Track invitation delivery status\n\n**References:** Requirements.md - Collaborator Management section", "linked_tasks": ["VIEW-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-003-4", "summary": "Implement wish list integration section", "description": "Add wish list integration features for viewing and importing birthday person's wish list items.\n\n**Acceptance Criteria:**\n- Display birthday person's wish list (if shared)\n- Add \"Add to Gift List\" buttons for wish list items\n- Implement wish list request functionality\n- Show integration status and permissions\n- Handle wish list privacy settings\n- Add bulk import options for multiple items\n\n**Wish List Integration:**\n```vue\n<template>\n  <div class=\"wish-list-integration\">\n    <div class=\"section-header\">\n      <h3 class=\"text-h6\">{{ birthdayPerson.name }}'s Wish List</h3>\n      <v-chip\n        :color=\"wishListStatus.color\"\n        size=\"small\"\n      >\n        {{ wishListStatus.text }}\n      </v-chip>\n      <v-spacer></v-spacer>\n      <v-btn\n        v-if=\"!hasWishListAccess\"\n        size=\"small\"\n        @click=\"requestWishListAccess\"\n      >\n        Request Access\n      </v-btn>\n    </div>\n    \n    <!-- Wish List Items -->\n    <div v-if=\"hasWishListAccess\" class=\"wish-list-items\">\n      <div\n        v-for=\"item in wishListItems\"\n        :key=\"item.id\"\n        class=\"wish-list-item\"\n      >\n        <div class=\"item-info\">\n          <h4>{{ item.title }}</h4>\n          <p class=\"text-body-2\">{{ item.description }}</p>\n          <div class=\"item-meta\">\n            <span v-if=\"item.price\">{{ formatPrice(item.price) }}</span>\n            <v-chip size=\"x-small\" :color=\"getPriorityColor(item.priority)\">\n              {{ item.priority }}\n            </v-chip>\n          </div>\n        </div>\n        <div class=\"item-actions\">\n          <v-btn\n            v-if=\"!isItemInGiftList(item)\"\n            size=\"small\"\n            color=\"primary\"\n            @click=\"addToGiftList(item)\"\n          >\n            Add to Gift List\n          </v-btn>\n          <v-chip v-else size=\"small\" color=\"success\">\n            Added\n          </v-chip>\n        </div>\n      </div>\n    </div>\n    \n    <!-- No Access State -->\n    <div v-else-if=\"!hasWishListAccess\" class=\"no-access-state\">\n      <v-icon size=\"48\" color=\"grey\">mdi-lock</v-icon>\n      <p class=\"text-body-1\">Wish list is private</p>\n      <p class=\"text-body-2\">Request access to see {{ birthdayPerson.name }}'s wish list</p>\n    </div>\n  </div>\n</template>\n```\n\n**Wish List States:**\n| State | Description | Actions Available |\n|-------|-------------|------------------|\n| Public | Wish list is publicly visible | View, Add items to gift list |\n| Shared | Shared with collaborators | View, Add items to gift list |\n| Private | Not shared | Request access |\n| Not Created | No wish list exists | Suggest creating one |\n\n**Integration Features:**\n- One-click add to gift list\n- Bulk selection and import\n- Duplicate detection and handling\n- Price and priority preservation\n- Link preservation from original wish\n\n**Access Management:**\n- Request access workflow\n- Notification to birthday person\n- Access approval/denial handling\n- Temporary access for gift list duration\n\n**References:** Requirements.md - Wish List Integration section", "linked_tasks": ["VIEW-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-003-5", "summary": "Add real-time features and activity feed", "description": "Implement real-time collaboration features and activity feed for the gift list detail page.\n\n**Acceptance Criteria:**\n- Add real-time updates for all gift list changes\n- Implement activity feed showing recent actions\n- Add live collaboration indicators (who's online, editing)\n- Include real-time notifications for important events\n- Implement conflict resolution for simultaneous edits\n- Add export and sharing functionality\n\n**Real-time Features:**\n```vue\n<template>\n  <div class=\"real-time-features\">\n    <!-- Live Collaboration Bar -->\n    <div v-if=\"activeCollaborators.length > 0\" class=\"collaboration-bar\">\n      <div class=\"active-users\">\n        <v-avatar\n          v-for=\"user in activeCollaborators\"\n          :key=\"user.id\"\n          size=\"24\"\n          class=\"mr-1\"\n        >\n          <v-img :src=\"user.avatar_url\" />\n          <v-tooltip activator=\"parent\" location=\"bottom\">\n            {{ user.name }} is online\n          </v-tooltip>\n        </v-avatar>\n      </div>\n      <div class=\"live-indicator\">\n        <v-icon size=\"12\" color=\"success\">mdi-circle</v-icon>\n        <span class=\"text-caption\">Live</span>\n      </div>\n    </div>\n    \n    <!-- Activity Feed -->\n    <div class=\"activity-feed\">\n      <h3 class=\"text-h6\">Recent Activity</h3>\n      <ActivityFeed\n        :gift-list-id=\"giftListId\"\n        :max-items=\"10\"\n        :real-time=\"true\"\n      />\n    </div>\n    \n    <!-- Export and Sharing -->\n    <div class=\"export-sharing\">\n      <v-menu>\n        <template #activator=\"{ props }\">\n          <v-btn v-bind=\"props\" variant=\"outlined\">\n            <v-icon left>mdi-export</v-icon>\n            Export & Share\n          </v-btn>\n        </template>\n        <v-list>\n          <v-list-item @click=\"exportToPDF\">\n            <v-list-item-title>Export to PDF</v-list-item-title>\n          </v-list-item>\n          <v-list-item @click=\"exportToCSV\">\n            <v-list-item-title>Export to CSV</v-list-item-title>\n          </v-list-item>\n          <v-list-item @click=\"shareGiftList\">\n            <v-list-item-title>Share Link</v-list-item-title>\n          </v-list-item>\n          <v-list-item @click=\"emailSummary\">\n            <v-list-item-title>Email Summary</v-list-item-title>\n          </v-list-item>\n        </v-list>\n      </v-menu>\n    </div>\n  </div>\n</template>\n```\n\n**Real-time Events:**\n- Item added/updated/deleted\n- Item reserved/purchased\n- Collaborator joined/left\n- Role changes\n- Wish list updates\n\n**Activity Feed Events:**\n```javascript\nconst ACTIVITY_TYPES = {\n  ITEM_ADDED: 'added item',\n  ITEM_RESERVED: 'reserved item',\n  ITEM_PURCHASED: 'purchased item',\n  COLLABORATOR_JOINED: 'joined collaboration',\n  ROLE_CHANGED: 'role changed',\n  WISH_LIST_UPDATED: 'updated wish list'\n}\n```\n\n**Conflict Resolution:**\n- Detect simultaneous edits\n- Show conflict resolution dialog\n- Provide merge options\n- Automatic conflict resolution for simple cases\n\n**Export Options:**\n- PDF summary with all items and status\n- CSV export for spreadsheet analysis\n- Shareable read-only link\n- Email summary to collaborators\n\n**Live Collaboration:**\n- Show who's currently viewing the page\n- Display editing indicators\n- Real-time cursor positions (future)\n- Live typing indicators\n\n**References:** Requirements.md - Real-time Collaboration and Export features", "linked_tasks": ["VIEW-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Task"}, {"id": "VIEW-004", "summary": "Create MyWishList<PERSON>iew", "description": "Create new view (/profile/wish-list) for personal wish list management. Include: wish list header with privacy controls, wish list items with visibility settings, sharing management section, integration status with gift lists. Follow existing view patterns with CRUD operations for wish list items, privacy settings management, and shareable link generation. Include real-time updates when items are added to gift lists.", "linked_tasks": ["STORE-004"], "epic": "Gift List UI", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "VIEW-005", "summary": "<PERSON><PERSON><PERSON> with collaboration features", "description": "Extend existing WishesView.vue to include collaboration features: gift list creation button, gift list integration section, real-time collaboration updates, collaboration status filtering. Add switch between personal wishes and collaborative gift list views. Maintain existing functionality while adding new collaboration features. Include proper permission handling for different user roles.", "linked_tasks": ["STORE-001", "STORE-003", "COMP-003"], "epic": "Gift List UI", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "ROUTE-001", "summary": "Add gift list routes to router", "description": "Extend existing router configuration (src/router/index.js) to include new gift list routes: /gift-lists (overview), /gift-lists/create, /gift-lists/{giftListId}, /gift-lists/{giftListId}/invite, /profile/wish-list. Include proper route guards for authentication, onboarding, and collaboration permissions. Follow existing route patterns with meta properties for requiresAuth and requiresOnboarding.", "linked_tasks": ["VIEW-001", "VIEW-002", "VIEW-003", "VIEW-004"], "epic": "Navigation & Routing", "priority": "High", "estimated_effort": "Small", "type": "Story"}, {"id": "NAV-001", "summary": "Update BottomNavigation with gift lists section", "description": "Extend existing BottomNavigation.vue component to include Gift Lists section in navigation. Add new navigation item with icon, badge for pending invitations, and proper active state handling. Maintain existing navigation patterns and responsive design. Include notification badges for pending invitations and new activity.", "linked_tasks": ["ROUTE-001", "STORE-002"], "epic": "Navigation & Routing", "priority": "Medium", "estimated_effort": "Small", "type": "Story"}, {"id": "EMAIL-001", "summary": "Create gift list invitation email templates", "description": "Create Brevo email templates for gift list collaboration: invitation to join gift list, invitation accepted/declined notifications, gift list activity summaries. Include personalized content with birthday person details, gift list information, and clear call-to-action buttons. Follow existing email template patterns and include tracking pixels for analytics.", "linked_tasks": ["API-002"], "epic": "Email Notifications", "priority": "Medium", "estimated_effort": "Medium", "type": "Story"}, {"id": "EMAIL-002", "summary": "Create collaboration activity email templates", "description": "Create email templates for collaboration activities: item reserved notifications, item purchased notifications, new item added to gift list, wish list shared notifications. Include digest options for multiple activities and user preference controls for email frequency. Integrate with existing Brevo email system and tracking.", "linked_tasks": ["RT-002"], "epic": "Email Notifications", "priority": "Low", "estimated_effort": "Medium", "type": "Story"}, {"id": "NOTIF-001", "summary": "Implement in-app collaboration notifications", "description": "Create in-app notification system for collaboration events using existing notification patterns. Include: invitation received, invitation accepted/declined, item reserved/purchased, new collaborator joined, wish list updated. Implement notification store, notification components, and real-time delivery. Include notification preferences and mark as read functionality.", "linked_tasks": ["RT-002", "STORE-002"], "epic": "In-App Notifications", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "SEC-001", "summary": "Implement collaboration security policies", "description": "Create comprehensive RLS policies for collaboration features: gift list access control, collaborator permission enforcement, wish list privacy controls, invitation security. Implement role-based access control (RBAC) for admin vs collaborator permissions. Include audit logging for sensitive actions and rate limiting for invitations to prevent abuse.", "linked_tasks": ["DB-001", "DB-002", "DB-003"], "epic": "Security & Permissions", "priority": "High", "estimated_effort": "Large", "type": "Story"}, {"id": "SEC-002", "summary": "Add collaboration permission middleware", "description": "Create permission checking middleware for API endpoints and UI components. Implement: gift list access validation, collaborator role verification, wish list privacy enforcement, invitation permission checks. Include proper error handling for permission denied scenarios and consistent permission checking patterns across the application.", "linked_tasks": ["API-001", "API-002", "SEC-001"], "epic": "Security & Permissions", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "TEST-001", "summary": "Create unit tests for gift list stores", "description": "Write comprehensive unit tests for gift list Pinia stores: useGiftListsStore, useCollaboratorsStore, extended useWishesStore, useWishListItemsStore. Test all actions, computed properties, error handling, and state mutations. Include mock data and API response testing. Follow existing testing patterns and achieve >90% code coverage.", "linked_tasks": ["STORE-001", "STORE-002", "STORE-003", "STORE-004"], "epic": "Testing", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "TEST-002", "summary": "Create component tests for collaboration UI", "description": "Write unit tests for all new collaboration components: GiftListCard, CollaboratorCard, GiftListItemCard, InviteCollaboratorsDialog, ActivityFeed. Test component props, events, user interactions, and real-time updates. Include accessibility testing and responsive design validation. Use Vue Test Utils and follow existing component testing patterns.", "linked_tasks": ["COMP-001", "COMP-002", "COMP-003", "COMP-004", "COMP-005"], "epic": "Testing", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "TEST-003", "summary": "Create integration tests for collaboration workflows", "description": "Write end-to-end integration tests for complete collaboration workflows: gift list creation, collaborator invitation, item reservation, purchase flow, real-time updates. Test cross-user scenarios, permission enforcement, and error handling. Include database state validation and real-time event verification. Use existing testing infrastructure.", "linked_tasks": ["VIEW-001", "VIEW-002", "VIEW-003", "RT-001", "RT-002"], "epic": "Testing", "priority": "Low", "estimated_effort": "XL", "type": "Story"}, {"id": "PERF-001", "summary": "Implement real-time connection optimization", "description": "Optimize real-time WebSocket connections for collaboration features. Implement: connection pooling, automatic reconnection with exponential backoff, message queuing for offline scenarios, connection state management. Include performance monitoring, memory leak prevention, and efficient event subscription management. Ensure minimal battery impact on mobile devices.", "linked_tasks": ["RT-001", "RT-002"], "epic": "Performance Optimization", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "PERF-002", "summary": "Add collaboration data caching and pagination", "description": "Implement efficient caching strategies for collaboration data: gift list caching, collaborator list caching, activity feed pagination, optimistic UI updates. Include cache invalidation strategies, background data refresh, and memory management. Follow existing pagination patterns from friends store and implement infinite scrolling for activity feeds.", "linked_tasks": ["STORE-001", "STORE-002", "COMP-005"], "epic": "Performance Optimization", "priority": "Medium", "estimated_effort": "Medium", "type": "Story"}, {"id": "CONFLICT-001", "summary": "Implement conflict resolution for concurrent edits", "description": "Create conflict resolution system for concurrent collaboration actions: simultaneous item reservations, conflicting gift list edits, concurrent collaborator management. Implement: optimistic locking, conflict detection, user-friendly conflict resolution UI, automatic retry mechanisms. Include proper error messaging and user guidance for conflict scenarios.", "linked_tasks": ["RT-002", "API-004"], "epic": "Conflict Resolution", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "MIGRATE-001", "summary": "Create database migration scripts", "description": "Create comprehensive database migration scripts for all collaboration schema changes: gift_lists table creation, gift_list_collaborators table creation, wish_list_items table creation, wishes table extensions, new indexes, RLS policies, database views. Include rollback scripts and data validation. Follow existing migration patterns in database/migrations/.", "linked_tasks": ["DB-001", "DB-002", "DB-003", "DB-004", "DB-005"], "epic": "Database Migration", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "DOC-001", "summary": "Create collaboration feature documentation", "description": "Create comprehensive documentation for collaboration features: API documentation, component usage guides, collaboration workflow documentation, troubleshooting guides. Include code examples, integration patterns, and best practices. Update existing documentation to reflect collaboration feature integration.", "linked_tasks": ["API-001", "API-002", "API-003", "API-004"], "epic": "Documentation", "priority": "Low", "estimated_effort": "Medium", "type": "Story"}, {"id": "DEPLOY-001", "summary": "Prepare collaboration features for deployment", "description": "Prepare collaboration features for production deployment: environment variable configuration, Supabase project setup, real-time subscription limits, email template deployment, performance monitoring setup. Include deployment checklist, rollback procedures, and monitoring alerts for collaboration-specific metrics.", "linked_tasks": ["MIGRATE-001", "EMAIL-001", "EMAIL-002", "PERF-001"], "epic": "Deployment", "priority": "Low", "estimated_effort": "Medium", "type": "Story"}]