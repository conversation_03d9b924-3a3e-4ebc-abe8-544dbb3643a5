{"name": "track-tasks", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "start": "node server.mjs", "test": "vitest", "test:ui": "vitest --ui", "api:start": "node api/server.js", "api:dev": "node --watch api/server.js", "api:test": "NODE_ENV=test vitest run api/"}, "dependencies": {"@fontsource/roboto": "5.2.5", "@mdi/font": "7.4.47", "compression": "^1.8.0", "cors": "^2.8.5", "express": "^4.17.1", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "markdown-it": "^14.1.0", "md-editor-v3": "^5.7.1", "morgan": "^1.10.0", "multer": "^2.0.2", "pocketbase": "^0.26.1", "vue": "^3.5.13", "vuetify": "^3.8.1"}, "devDependencies": {"@pinia/testing": "^1.0.2", "@vitejs/plugin-vue": "^5.2.3", "@vitest/ui": "^3.2.4", "eslint": "^9.23.0", "eslint-config-vuetify": "^4.0.0", "globals": "^16.0.0", "happy-dom": "^18.0.1", "pinia": "^3.0.1", "sass-embedded": "^1.86.3", "unplugin-auto-import": "^19.1.1", "unplugin-fonts": "^1.3.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-router": "^0.12.0", "vite": "^6.2.2", "vite-plugin-vue-layouts-next": "^0.0.8", "vite-plugin-vuetify": "^2.1.1", "vitest": "^3.2.4", "vue-router": "^4.5.0"}}