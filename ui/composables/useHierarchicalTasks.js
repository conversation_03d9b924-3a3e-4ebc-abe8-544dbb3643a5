// src/composables/useHierarchicalTasks.js

// This composable extracts the hierarchical task processing logic.
// It can be used by any component or store that needs to display tasks in a hierarchy.

export function useHierarchicalTasks() {
  const getHierarchicalTasks = allTasks => {
    if (!allTasks || allTasks.length === 0) {
      return [];
    }

    // Create a mutable copy of tasks and ensure each task has a children array initialized
    const taskMap = new Map(
      allTasks.map(task => [
        task.task_id,
        { ...task, children: [], incomingDependencies: [] },
      ])
    );

    // Build graph: identify children and incoming 'Requires' dependencies
    for (const task of allTasks) {
      const currentTaskNode = taskMap.get(task.task_id);
      if (!currentTaskNode) continue;

      // Populate children based on parent_id
      if (task.parent_id && taskMap.has(task.parent_id)) {
        const parentNode = taskMap.get(task.parent_id);
        parentNode.children.push(task.task_id);
      }

      // Populate incomingDependencies for 'Requires' links
      if (Array.isArray(task.linked_tasks)) {
        for (const linkedTask of task.linked_tasks) {
          // Ensure linkedTask and linkedTask.task_id are defined
          if (linkedTask && linkedTask.task_id && linkedTask.linkType === 'Requires' && taskMap.has(linkedTask.task_id)) {
            const dependencyNode = taskMap.get(linkedTask.task_id);
            if (dependencyNode) { // Ensure dependencyNode exists
                 dependencyNode.incomingDependencies.push(task.task_id);
            }
          }
        }
      }
    }

    const orderedList = [];
    const visited = new Set();
    const recursionStack = new Set(); // For cycle detection

    const dfs = (taskId, level) => {
      if (recursionStack.has(taskId)) {
        console.warn(`Cycle detected in task hierarchy/dependencies involving: ${taskId}. Skipping to prevent infinite loop.`);
        return; // Break cycle
      }
      if (visited.has(taskId)) {
        return;
      }

      recursionStack.add(taskId);
      visited.add(taskId);

      const taskNode = taskMap.get(taskId);
      if (!taskNode) {
        recursionStack.delete(taskId);
        return; // Task not found in map, should not happen if allTasks is consistent
      }

      // First, process tasks that this task 'Requires' (dependencies)
      // These should appear before the current task in the ordered list if possible,
      // or at least ensure they are processed.
      if (Array.isArray(taskNode.linked_tasks)) {
        for (const linkedTask of taskNode.linked_tasks) {
           if (linkedTask && linkedTask.task_id && linkedTask.linkType === 'Requires' && taskMap.has(linkedTask.task_id)) {
            // DFS on dependency. Level management for dependencies can be tricky.
            // For simplicity here, we process them at the same or a higher level if they aren't parents.
            // If a dependency is also a parent, the parent-child relationship will primarily dictate its position.
            dfs(linkedTask.task_id, level);
          }
        }
      }

      // Add the current task to the list with its determined level
      orderedList.push({ ...taskNode, level });

      // Then, process its direct children, increasing their level
      // Sort children by task_id for a consistent order
      const sortedChildren = [...taskNode.children].sort((a, b) => a.localeCompare(b));
      for (const childId of sortedChildren) {
        dfs(childId, level + 1);
      }

      recursionStack.delete(taskId);
    };

    // Determine initial root nodes for DFS traversal.
    // Roots are tasks that:
    // 1. Do not have a `parent_id`.
    // 2. Do not have any incoming 'Requires' dependencies from other tasks that are not their children.
    // This definition of roots is complex with interdependencies.
    // A simpler approach often starts with all tasks that have no parent_id.
    // The DFS will then handle the order based on dependencies.

    const initialRoots = allTasks
      .filter(task => !task.parent_id) // Start with tasks that don't have explicit parents
      .sort((a, b) => a.task_id.localeCompare(b.task_id)); // Sort roots for consistent starting order

    for (const root of initialRoots) {
      if (!visited.has(root.task_id)) {
        dfs(root.task_id, 0);
      }
    }

    // Process any remaining tasks that were not reached (e.g., part of orphaned cycles or disconnected graphs)
    // These will be treated as new roots at level 0.
    for (const task of allTasks) {
      if (!visited.has(task.task_id)) {
        console.warn(`Task ${task.task_id} was not visited as part of the main hierarchy. Adding as a root.`);
        dfs(task.task_id, 0);
      }
    }
    return orderedList;
  };

  return {
    getHierarchicalTasks,
  };
}
