/**
 * Notification composable
 * Provides a reusable way to show snackbar notifications across components
 */

import { ref } from 'vue'

/**
 * Creates a notification system for a component
 * @param {Object} options - Configuration options
 * @param {number} options.timeout - Default timeout in milliseconds
 * @returns {Object} Notification system interface
 */
export function useNotification(options = {}) {
  const { timeout = 3000 } = options

  // Reactive state
  const show = ref(false)
  const message = ref('')
  const color = ref('info')
  const currentTimeout = ref(timeout)

  /**
   * Shows a notification
   * @param {string} text - The message to display
   * @param {string} type - Type of notification (success, error, warning, info)
   * @param {number} duration - Custom timeout duration
   */
  const showNotification = (text, type = 'info', duration = timeout) => {
    message.value = text
    color.value = getColorForType(type)
    currentTimeout.value = duration
    show.value = true
  }

  /**
   * Shows a success notification
   * @param {string} text - The success message
   * @param {number} duration - Custom timeout duration
   */
  const showSuccess = (text, duration = timeout) => {
    showNotification(text, 'success', duration)
  }

  /**
   * Shows an error notification
   * @param {string} text - The error message
   * @param {number} duration - Custom timeout duration
   */
  const showError = (text, duration = 5000) => { // Longer default for errors
    showNotification(text, 'error', duration)
  }

  /**
   * Shows a warning notification
   * @param {string} text - The warning message
   * @param {number} duration - Custom timeout duration
   */
  const showWarning = (text, duration = timeout) => {
    showNotification(text, 'warning', duration)
  }

  /**
   * Shows an info notification
   * @param {string} text - The info message
   * @param {number} duration - Custom timeout duration
   */
  const showInfo = (text, duration = timeout) => {
    showNotification(text, 'info', duration)
  }

  /**
   * Hides the current notification
   */
  const hide = () => {
    show.value = false
  }

  /**
   * Maps notification types to Vuetify colors
   * @param {string} type - Notification type
   * @returns {string} Vuetify color name
   */
  const getColorForType = (type) => {
    const colorMap = {
      success: 'success',
      error: 'error',
      warning: 'warning',
      info: 'info',
    }
    return colorMap[type] || 'info'
  }

  return {
    // State
    show,
    message,
    color,
    timeout: currentTimeout,

    // Methods
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hide,
  }
}

/**
 * Creates a global notification system that can be used across multiple components
 * This is useful for notifications that should persist across route changes
 */
export function useGlobalNotification() {
  // Global state (will persist across component unmounts)
  const globalState = {
    show: ref(false),
    message: ref(''),
    color: ref('info'),
    timeout: ref(3000),
    queue: ref([]),
  }

  /**
   * Adds a notification to the global queue
   * @param {string} text - The message to display
   * @param {string} type - Type of notification
   * @param {number} duration - Timeout duration
   */
  const queueNotification = (text, type = 'info', duration = 3000) => {
    const notification = {
      id: Date.now() + Math.random(),
      text,
      type,
      duration,
      color: getColorForType(type),
    }

    globalState.queue.value.push(notification)
    
    if (!globalState.show.value) {
      showNext()
    }
  }

  /**
   * Shows the next notification in the queue
   */
  const showNext = () => {
    if (globalState.queue.value.length === 0) {
      globalState.show.value = false
      return
    }

    const notification = globalState.queue.value.shift()
    globalState.message.value = notification.text
    globalState.color.value = notification.color
    globalState.timeout.value = notification.duration
    globalState.show.value = true
  }

  /**
   * Hides current notification and shows next in queue
   */
  const hideAndShowNext = () => {
    globalState.show.value = false
    // Small delay to allow transition
    setTimeout(showNext, 300)
  }

  /**
   * Maps notification types to colors
   */
  const getColorForType = (type) => {
    const colorMap = {
      success: 'success',
      error: 'error',
      warning: 'warning',
      info: 'info',
    }
    return colorMap[type] || 'info'
  }

  /**
   * Clears all notifications from the queue
   */
  const clearQueue = () => {
    globalState.queue.value = []
    globalState.show.value = false
  }

  return {
    // State
    show: globalState.show,
    message: globalState.message,
    color: globalState.color,
    timeout: globalState.timeout,
    queueLength: globalState.queue.length,

    // Methods
    showSuccess: (text, duration) => queueNotification(text, 'success', duration),
    showError: (text, duration) => queueNotification(text, 'error', duration || 5000),
    showWarning: (text, duration) => queueNotification(text, 'warning', duration),
    showInfo: (text, duration) => queueNotification(text, 'info', duration),
    hide: hideAndShowNext,
    clearQueue,
  }
}

/**
 * Predefined notification messages for common operations
 */
export const notificationMessages = {
  // CRUD operations
  created: (entityName) => `${entityName} created successfully`,
  updated: (entityName) => `${entityName} updated successfully`,
  deleted: (entityName) => `${entityName} deleted successfully`,
  
  // Task-specific messages
  taskAdded: 'Task added successfully',
  taskUpdated: 'Task updated successfully',
  taskDeleted: 'Task deleted successfully',
  taskLinked: 'Task linked successfully',
  taskUnlinked: 'Task unlinked successfully',
  
  // Project-specific messages
  projectCreated: 'Project created successfully',
  projectUpdated: 'Project updated successfully',
  projectDeleted: 'Project deleted successfully',
  
  // Upload messages
  uploadStarted: 'Upload started...',
  uploadProgress: (count) => `Uploaded ${count} items...`,
  uploadComplete: (count) => `Successfully uploaded ${count} items`,
  uploadFailed: 'Upload failed',
  
  // Error messages
  networkError: 'Network error occurred. Please try again.',
  validationError: 'Please check your input and try again',
  permissionError: 'You do not have permission to perform this action',
  notFound: 'The requested item was not found',
  
  // General messages
  saved: 'Changes saved successfully',
  cancelled: 'Operation cancelled',
  loading: 'Loading...',
  noChanges: 'No changes to save',
}
