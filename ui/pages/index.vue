<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON>er -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">My Tasks</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Manage and track your project tasks
            </p>
          </div>
          <div class="d-flex align-center ga-2">
            <v-btn
              color="primary"
              prepend-icon="mdi-plus"
              variant="elevated"
              @click="$router.push('/tasks/new')"
            >
              Add Task
            </v-btn>
            <v-btn
              color="primary"
              prepend-icon="mdi-file-upload"
              variant="elevated"
              @click="$router.push('/upload')"
            >
              Import Tasks
            </v-btn>
            <v-btn
              :color="isHierarchicalView ? 'secondary' : 'primary'"
              :prepend-icon="isHierarchicalView ? 'mdi-format-list-bulleted' : 'mdi-sitemap'"
              variant="elevated"
              @click="toggleHierarchicalView"
            >
              {{ isHierarchicalView ? 'Flat View' : 'Hierarchical View' }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Project Selector -->
    <ProjectSelector
      v-model="selectedProjectId"
      @project-changed="onProjectChanged"
    />

    <!-- Statistics Cards -->
    <DashboardStats />

    <!-- Filters and Search -->
    <TaskFilters
      v-if="projectFilteredTasks.length > 0"
      v-model="filters"
      v-model:view-mode="viewMode"
      :tasks="projectFilteredTasks"
      :total-tasks-count="projectFilteredTasks.length"
    />

    <!-- Tasks List -->
    <v-row v-if="filteredTasks.length > 0">
      <v-col>
        <v-card elevation="2">
          <v-card-title>
            Tasks ({{ filteredTasks.length }})
          </v-card-title>

          <!-- List View -->
          <div v-if="viewMode === 'list'">
            <v-list>
              <template v-for="(task, index) in filteredTasks" :key="task.task_id">
                <TaskListItem
                  :task="task"
                  @update-status="updateStatus"
                  @view-task-details="viewTaskDetails"
                />
                <v-divider v-if="index < filteredTasks.length - 1" />
              </template>
            </v-list>
          </div>

          <!-- Grid View -->
          <div v-else class="pa-4">
            <v-row>
              <v-col
                v-for="task in filteredTasks"
                :key="task.task_id"
                cols="12"
                lg="4"
                md="6"
              >
                <TaskGridItem
                  :task="task"
                  @update-status="updateStatus"
                  @view-task-details="viewTaskDetails"
                />
              </v-col>
            </v-row>
          </div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <v-row v-else-if="tasksStore.tasks.length === 0 && !tasksStore.loading">
      <v-col>
        <v-card class="text-center pa-12" elevation="1">
          <v-icon class="mb-4" color="grey-lighten-2" size="120">
            mdi-format-list-checks-outline
          </v-icon>
          <h2 class="text-h5 mb-4">No Tasks Yet</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Import your first JSON file to start managing tasks.
          </p>
          <v-btn
            color="primary"
            size="large"
            variant="elevated"
            @click="$router.push('/upload')"
          >
            <v-icon start>mdi-file-upload</v-icon>
            Import Tasks
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- No Results Found -->
    <v-row v-else-if="filteredTasks.length === 0 && tasksStore.tasks.length > 0">
      <v-col>
        <v-card class="text-center pa-8" elevation="1">
          <v-icon class="mb-4" color="grey-lighten-2" size="64">
            mdi-filter-remove-outline
          </v-icon>
          <h2 class="text-h6 mb-4">No Tasks Match Your Filters</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Try adjusting your search terms or filters to find tasks.
          </p>
          <v-btn
            variant="outlined"
            @click="clearFilters"
          >
            Clear Filters
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="tasksStore.loading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup>
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import DashboardStats from '@/components/dashboard/DashboardStats.vue'
  import TaskFilters from '@/components/TaskFilters.vue'
  import TaskGridItem from '@/components/tasks/TaskGridItem.vue'
  import TaskListItem from '@/components/tasks/TaskListItem.vue'
  import ProjectSelector from '@/components/projects/ProjectSelector.vue'
  import { useTasksStore } from '@/stores/tasks'
  import { useProjectsStore } from '@/stores/projects'
  // Display utils are not directly needed here anymore as they are used in child components
  // import { getPriorityColor, getStatusColor, getTypeIcon, truncateText } from '@/utils/taskDisplayUtils';

  // Router
  const router = useRouter()

  // Stores
  const tasksStore = useTasksStore()
  const projectsStore = useProjectsStore()

  // Reactive data
  const viewMode = ref('list')
  const isHierarchicalView = ref(true)
  const selectedProjectId = ref(null)

  const filters = ref({
    search: '',
    type: 'All',
    priority: 'All',
    status: 'All',
    epic: 'All',
  })

  // Computed properties
  // Tasks filtered by project only (for TaskFilters component)
  const projectFilteredTasks = computed(() => {
    let tasks = tasksStore.tasks
    console.log('Total tasks from store:', tasks.length)
    console.log('Selected project ID for filtering:', selectedProjectId.value)
    
    // Filter by selected project first
    if (selectedProjectId.value) {
      tasks = tasks.filter(task => {
        console.log(`Task ${task.task_id} project_id:`, task.project_id, 'matches:', task.project_id === selectedProjectId.value)
        return task.project_id === selectedProjectId.value
      })
      console.log('Tasks after project filtering:', tasks.length)
    }
    
    return tasks
  })

  // Fully filtered tasks (for display)
  const filteredTasks = computed(() => {
    let tasks = projectFilteredTasks.value

    // Apply search
    if (filters.value.search) {
      tasks = tasksStore.searchTasks(tasks, filters.value.search)
    }

    // Apply filters
    tasks = tasksStore.filterTasks(tasks, filters.value)

    // Apply hierarchical sorting if enabled
    if (isHierarchicalView.value) {
      tasks = tasksStore.getHierarchicalTasks(tasks)
    }

    return tasks
  })

  // Methods
  const toggleHierarchicalView = () => {
    isHierarchicalView.value = !isHierarchicalView.value
  }

  const viewTaskDetails = taskId => {
    router.push(`/tasks/${taskId}`)
  }

  const onProjectChanged = (project) => {
    // Project change is handled by the v-model binding
    console.log('Project changed to:', project?.name || 'None')
    console.log('Selected project ID:', selectedProjectId.value)
    console.log('Project filtered tasks count:', projectFilteredTasks.value.length)
    console.log('Fully filtered tasks count:', filteredTasks.value.length)
  }

  const updateStatus = async (task, newStatus) => {
    try {
      await tasksStore.updateTaskStatus(task.task_id, newStatus)
      // The computed filteredTasks will automatically update when tasksStore.tasks changes
      // No need to manually trigger filter refresh
    } catch (error) {
      console.error('Failed to update task status:', error)
    }
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      type: 'All',
      priority: 'All',
      status: 'All',
      epic: 'All',
    }
  }

  // Helper functions like getPriorityColor, getStatusColor, getTypeIcon, truncateText
  // are no longer needed here as they are encapsulated in TaskListItem.vue and TaskGridItem.vue

  // Lifecycle
  onMounted(async () => {
    // Fetch both projects and tasks
    await Promise.all([
      projectsStore.fetchProjects(),
      tasksStore.fetchTasks(),
    ])

    // Set initial project selection
    if (projectsStore.selectedProject) {
      selectedProjectId.value = projectsStore.selectedProject.id
    } else if (projectsStore.projects.length > 0) {
      selectedProjectId.value = projectsStore.projects[0].id
      projectsStore.selectProject(projectsStore.projects[0])
    }

    // Load filters from localStorage
    const savedFilters = localStorage.getItem('taskFilters')
    if (savedFilters) {
      filters.value = JSON.parse(savedFilters)
    }
  })

  onBeforeUnmount(() => {
    // Save filters to localStorage before component is unmounted
    localStorage.setItem('taskFilters', JSON.stringify(filters.value))
  })
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}

.v-chip {
  font-weight: 500;
}

.text-wrap {
  word-break: break-word;
  white-space: normal;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
