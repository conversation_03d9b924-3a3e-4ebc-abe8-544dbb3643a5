<template>
  <div class="forgot-password-page">
    <v-container class="fill-height">
      <v-row justify="center" align="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card elevation="8" rounded="lg">
            <v-card-title class="text-h4 text-center pa-6">
              Reset Password
            </v-card-title>
            
            <v-card-text class="pa-6">
              <p class="text-body-1 text-center mb-6 text-medium-emphasis">
                Enter your email address and we'll send you a link to reset your password.
              </p>
              
              <v-form
                ref="form"
                v-model="valid"
                @submit.prevent="handleSubmit"
              >
                <v-text-field
                  v-model="email"
                  :rules="emailRules"
                  label="Email Address"
                  type="email"
                  variant="outlined"
                  prepend-inner-icon="mdi-email"
                  class="mb-4"
                  :disabled="loading"
                  required
                />
                
                <v-alert
                  v-if="error"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  closable
                  @click:close="clearError"
                >
                  {{ error }}
                </v-alert>
                
                <v-alert
                  v-if="success"
                  type="success"
                  variant="tonal"
                  class="mb-4"
                >
                  Password reset link has been sent to your email address.
                </v-alert>
                
                <v-btn
                  type="submit"
                  :loading="loading"
                  :disabled="!valid || loading"
                  color="primary"
                  size="large"
                  block
                  class="mb-4"
                >
                  Send Reset Link
                </v-btn>
              </v-form>
            </v-card-text>
            
            <v-card-actions class="pa-6 pt-0">
              <v-row justify="center">
                <v-col cols="auto">
                  <router-link
                    to="/login"
                    class="text-decoration-none"
                  >
                    <v-btn
                      variant="text"
                      color="primary"
                      prepend-icon="mdi-arrow-left"
                    >
                      Back to Login
                    </v-btn>
                  </router-link>
                </v-col>
              </v-row>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuthStore } from '../stores/auth.js'

const authStore = useAuthStore()

const form = ref(null)
const valid = ref(false)
const email = ref('')
const success = ref(false)

const loading = computed(() => authStore.isLoading)
const error = computed(() => authStore.authError)

const emailRules = [
  v => !!v || 'Email is required',
  v => /.+@.+\..+/.test(v) || 'Email must be valid',
]

const handleSubmit = async () => {
  if (!form.value) return
  
  const { valid: isValid } = await form.value.validate()
  if (!isValid) return
  
  const result = await authStore.requestPasswordReset(email.value)
  
  if (result.success) {
    success.value = true
    email.value = ''
    form.value.reset()
  }
}

const clearError = () => {
  authStore.clearError()
}
</script>

<style scoped>
.forgot-password-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.v-card {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}
</style>
