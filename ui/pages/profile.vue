<template>
  <v-container>
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-text-field
        v-model="userData.name"
        :rules="nameRules"
        label="Name"
        required
      ></v-text-field>
      <v-text-field
        v-model="userData.email"
        :rules="emailRules"
        label="Email"
        required
      ></v-text-field>
      <v-file-input
        v-model="userData.avatar"
        label="Avatar"
        show-size
        accept="image/*"
      ></v-file-input>
      <v-btn :loading="loading" :disabled="!valid" @click="updateProfile">
        Save Changes
      </v-btn>
    </v-form>
  </v-container>
</template>

<script>
import { ref, reactive } from 'vue';
import { useStore } from "../stores/auth";

export default {
  name: 'Profile',
  setup() {
    const store = useStore();

    const userData = reactive({
      name: store.user.name,
      email: store.user.email,
      avatar: null,
    });

    const loading = ref(false);
    const valid = ref(false);

    const nameRules = [v => !!v || 'Name is required'];
    const emailRules = [v => !!v || 'Email is required', v => /.+@.+\..+/.test(v) || 'E-mail must be valid'];

    const updateProfile = async () => {
      if (loading.value) return;
      loading.value = true;

      try {
        await store.updateUserProfile(userData);
        // Handle success: show notification or update local state
      } catch (error) {
        console.error(error);
        // Handle error: show notification
      } finally {
        loading.value = false;
      }
    };

    return { userData, valid, loading, nameRules, emailRules, updateProfile };
  }
}
</script>

<style scoped>
/* Add your styles here */
</style>

