<template>
  <div class="reset-password-page">
    <v-container class="fill-height">
      <v-row justify="center" align="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card elevation="8" rounded="lg">
            <v-card-title class="text-h4 text-center pa-6">
              Set New Password
            </v-card-title>
            
            <v-card-text class="pa-6">
              <p class="text-body-1 text-center mb-6 text-medium-emphasis">
                Enter your new password below.
              </p>
              
              <v-form
                ref="form"
                v-model="valid"
                @submit.prevent="handleSubmit"
              >
                <v-text-field
                  v-model="password"
                  :rules="passwordRules"
                  label="New Password"
                  :type="showPassword ? 'text' : 'password'"
                  variant="outlined"
                  prepend-inner-icon="mdi-lock"
                  :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showPassword = !showPassword"
                  class="mb-4"
                  :disabled="loading"
                  required
                />
                
                <v-text-field
                  v-model="confirmPassword"
                  :rules="confirmPasswordRules"
                  label="Confirm New Password"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  variant="outlined"
                  prepend-inner-icon="mdi-lock-check"
                  :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showConfirmPassword = !showConfirmPassword"
                  class="mb-4"
                  :disabled="loading"
                  required
                />
                
                <v-alert
                  v-if="error"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  closable
                  @click:close="clearError"
                >
                  {{ error }}
                </v-alert>
                
                <v-alert
                  v-if="success"
                  type="success"
                  variant="tonal"
                  class="mb-4"
                >
                  Your password has been successfully reset. You can now log in with your new password.
                </v-alert>
                
                <v-btn
                  type="submit"
                  :loading="loading"
                  :disabled="!valid || loading"
                  color="primary"
                  size="large"
                  block
                  class="mb-4"
                >
                  Reset Password
                </v-btn>
              </v-form>
            </v-card-text>
            
            <v-card-actions class="pa-6 pt-0">
              <v-row justify="center">
                <v-col cols="auto">
                  <router-link
                    to="/login"
                    class="text-decoration-none"
                  >
                    <v-btn
                      variant="text"
                      color="primary"
                      prepend-icon="mdi-arrow-left"
                    >
                      Back to Login
                    </v-btn>
                  </router-link>
                </v-col>
              </v-row>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const form = ref(null)
const valid = ref(false)
const password = ref('')
const confirmPassword = ref('')
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const success = ref(false)
const token = ref('')

const loading = computed(() => authStore.isLoading)
const error = computed(() => authStore.authError)

const passwordRules = [
  v => !!v || 'Password is required',
  v => (v && v.length >= 8) || 'Password must be at least 8 characters long',
  v => /(?=.*[a-z])/.test(v) || 'Password must contain at least one lowercase letter',
  v => /(?=.*[A-Z])/.test(v) || 'Password must contain at least one uppercase letter',
  v => /(?=.*\d)/.test(v) || 'Password must contain at least one number',
]

const confirmPasswordRules = [
  v => !!v || 'Please confirm your password',
  v => v === password.value || 'Passwords do not match',
]

const handleSubmit = async () => {
  if (!form.value) return
  
  const { valid: isValid } = await form.value.validate()
  if (!isValid) return
  
  const result = await authStore.resetPassword(token.value, password.value)
  
  if (result.success) {
    success.value = true
    password.value = ''
    confirmPassword.value = ''
    form.value.reset()
    
    // Redirect to login after 3 seconds
    setTimeout(() => {
      router.push('/login')
    }, 3000)
  }
}

const clearError = () => {
  authStore.clearError()
}

// Get token from URL query parameters
onMounted(() => {
  token.value = route.query.token || ''
  
  // If no token is provided, redirect to forgot password page
  if (!token.value) {
    router.push('/forgot-password')
  }
})
</script>

<style scoped>
.reset-password-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.v-card {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}
</style>
