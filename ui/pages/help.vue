<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h3 font-weight-bold mb-2">Help & Documentation</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Learn how to use Track Tasks effectively
        </p>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" lg="8">
        <!-- Getting Started -->
        <v-card class="mb-6" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="primary">mdi-rocket-launch</v-icon>
            Getting Started
          </v-card-title>

          <v-card-text>
            <v-stepper
              v-model="currentStep"
              alt-labels
              non-linear
            >
              <v-stepper-header>
                <v-stepper-item
                  icon="mdi-file-upload"
                  title="Upload Tasks"
                  value="1"
                />
                <v-divider />
                <v-stepper-item
                  icon="mdi-format-list-checks"
                  title="Manage Tasks"
                  value="2"
                />
                <v-divider />
                <v-stepper-item
                  icon="mdi-chart-line"
                  title="Track Progress"
                  value="3"
                />
              </v-stepper-header>

              <v-stepper-window>
                <v-stepper-window-item value="1">
                  <div class="pa-4">
                    <h3 class="text-h6 mb-3">1. Upload Your Tasks</h3>
                    <p class="mb-3">Start by uploading a JSON file containing your project tasks.</p>
                    <v-list>
                      <v-list-item prepend-icon="mdi-check">
                        Go to the Upload page
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Select a JSON file with your tasks
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Click "Process Upload" to import
                      </v-list-item>
                    </v-list>
                    <v-btn
                      class="mt-3"
                      color="primary"
                      variant="outlined"
                      @click="$router.push('/upload')"
                    >
                      Go to Upload
                    </v-btn>
                  </div>
                </v-stepper-window-item>

                <v-stepper-window-item value="2">
                  <div class="pa-4">
                    <h3 class="text-h6 mb-3">2. Manage Your Tasks</h3>
                    <p class="mb-3">View, filter, and update your tasks on the main dashboard.</p>
                    <v-list>
                      <v-list-item prepend-icon="mdi-check">
                        Filter tasks by type, priority, status, or epic
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Search tasks by ID, summary, or description
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Click on tasks to view detailed information
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Update task statuses and priorities
                      </v-list-item>
                    </v-list>
                    <v-btn
                      class="mt-3"
                      color="primary"
                      variant="outlined"
                      @click="$router.push('/')"
                    >
                      View Tasks
                    </v-btn>
                  </div>
                </v-stepper-window-item>

                <v-stepper-window-item value="3">
                  <div class="pa-4">
                    <h3 class="text-h6 mb-3">3. Track Your Progress</h3>
                    <p class="mb-3">Monitor project progress with real-time statistics and insights.</p>
                    <v-list>
                      <v-list-item prepend-icon="mdi-check">
                        View task completion statistics
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Track tasks by status and priority
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Navigate between linked tasks
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-check">
                        Export or clear data as needed
                      </v-list-item>
                    </v-list>
                  </div>
                </v-stepper-window-item>
              </v-stepper-window>
            </v-stepper>
          </v-card-text>
        </v-card>

        <!-- JSON File Format -->
        <v-card class="mb-6" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="warning">mdi-code-json</v-icon>
            JSON File Format
          </v-card-title>

          <v-card-text>
            <p class="mb-4">Your JSON file should contain an array of task objects with the following structure:</p>

            <v-code class="mb-4">
              <pre>{{ jsonExample }}</pre>
            </v-code>

            <v-row class="mt-4">
              <v-col cols="12" md="6">
                <h4 class="text-subtitle-1 font-weight-bold mb-2">Required Fields</h4>
                <v-chip-group>
                  <v-chip color="error" size="small" variant="outlined">id</v-chip>
                  <v-chip color="error" size="small" variant="outlined">summary</v-chip>
                </v-chip-group>
              </v-col>
              <v-col cols="12" md="6">
                <h4 class="text-subtitle-1 font-weight-bold mb-2">Optional Fields</h4>
                <v-chip-group>
                  <v-chip size="small" variant="outlined">description</v-chip>
                  <v-chip size="small" variant="outlined">linked_tasks</v-chip>
                  <v-chip size="small" variant="outlined">epic</v-chip>
                  <v-chip size="small" variant="outlined">priority</v-chip>
                  <v-chip size="small" variant="outlined">estimated_effort</v-chip>
                  <v-chip size="small" variant="outlined">type</v-chip>
                </v-chip-group>
              </v-col>
            </v-row>

            <v-btn
              class="mt-4"
              prepend-icon="mdi-download"
              variant="outlined"
              @click="downloadExample"
            >
              Download Example File
            </v-btn>
          </v-card-text>
        </v-card>

        <!-- Features Overview -->
        <v-card class="mb-6" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="success">mdi-star</v-icon>
            Key Features
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col v-for="feature in features" :key="feature.title" cols="12" md="6">
                <div class="d-flex align-start mb-4">
                  <v-avatar class="me-3" :color="feature.color" size="40">
                    <v-icon color="white">{{ feature.icon }}</v-icon>
                  </v-avatar>
                  <div>
                    <h4 class="text-subtitle-1 font-weight-bold">{{ feature.title }}</h4>
                    <p class="text-body-2 text-medium-emphasis">{{ feature.description }}</p>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Troubleshooting -->
        <v-card elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="error">mdi-help-circle</v-icon>
            Troubleshooting
          </v-card-title>

          <v-card-text>
            <v-expansion-panels variant="accordion">
              <v-expansion-panel
                v-for="faq in troubleshooting"
                :key="faq.question"
                :title="faq.question"
              >
                <v-expansion-panel-text>
                  <div v-html="faq.answer" />
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Quick Actions Sidebar -->
      <v-col cols="12" lg="4">
        <v-card class="mb-4" elevation="2">
          <v-card-title>Quick Actions</v-card-title>

          <v-card-text>
            <v-list>
              <v-list-item
                class="cursor-pointer"
                prepend-icon="mdi-file-upload"
                subtitle="Import tasks from JSON file"
                title="Upload Tasks"
                @click="$router.push('/upload')"
              />
              <v-list-item
                class="cursor-pointer"
                prepend-icon="mdi-format-list-checks"
                subtitle="Manage and track your tasks"
                title="View Tasks"
                @click="$router.push('/')"
              />
              <v-list-item
                class="cursor-pointer"
                prepend-icon="mdi-download"
                subtitle="Get a sample JSON file"
                title="Download Example"
                @click="downloadExample"
              />
            </v-list>
          </v-card-text>
        </v-card>

        <!-- Task Statistics -->
        <TaskStatistics />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { ref } from 'vue'
  import TaskStatistics from '@/components/TaskStatistics.vue'

  const currentStep = ref('1')

  const jsonExample = ref(`[
  {
    "id": "TASK-001",
    "summary": "Implement user login",
    "description": "Create login form with validation",
    "linked_tasks": ["TASK-002"],
    "epic": "Authentication",
    "priority": "High",
    "estimated_effort": "Medium",
    "type": "Story"
  },
  {
    "id": "TASK-002",
    "summary": "Setup database schema",
    "description": "Design and implement user tables",
    "linked_tasks": [],
    "epic": "Database",
    "priority": "High",
    "estimated_effort": "Large",
    "type": "Task"
  }
]`)

  const features = ref([
    {
      icon: 'mdi-database-import',
      title: 'JSON File Import',
      description: 'Upload and import task data from JSON files with validation and error handling.',
      color: 'primary',
    },
    {
      icon: 'mdi-filter-variant',
      title: 'Advanced Filtering',
      description: 'Filter and search tasks by type, priority, status, epic, and custom criteria.',
      color: 'success',
    },
    {
      icon: 'mdi-database-outline',
      title: 'Local Storage',
      description: 'Tasks are stored locally in your browser using IndexedDB for offline access.',
      color: 'info',
    },
    {
      icon: 'mdi-chart-line',
      title: 'Task Analytics',
      description: 'View statistics and insights about your project tasks and completion rates.',
      color: 'warning',
    },
    {
      icon: 'mdi-markdown',
      title: 'Markdown Support',
      description: 'Rich text rendering for task descriptions with full markdown support.',
      color: 'purple',
    },
    {
      icon: 'mdi-responsive',
      title: 'Responsive Design',
      description: 'Modern Vuetify UI that works seamlessly on desktop and mobile devices.',
      color: 'teal',
    },
  ])

  const troubleshooting = ref([
    {
      question: 'My JSON file won\'t upload',
      answer: '<p>Make sure your JSON file is valid and contains an array of task objects. Check that:</p><ul><li>Each task has required fields: <code>id</code> and <code>summary</code></li><li>The file is valid JSON syntax</li><li>The file size isn\'t too large for your browser</li></ul>',
    },
    {
      question: 'Tasks disappeared after closing browser',
      answer: '<p>Tasks are stored in your browser\'s IndexedDB. They may disappear if:</p><ul><li>You\'re using incognito/private browsing mode</li><li>Browser storage was cleared</li><li>You\'re on a different browser or device</li></ul><p>Always keep backup copies of your JSON files.</p>',
    },
    {
      question: 'Filter or search not working',
      answer: '<p>Try these troubleshooting steps:</p><ul><li>Clear search terms and reset filters</li><li>Refresh the page</li><li>Check if tasks were properly imported</li><li>Verify task data contains the fields you\'re filtering by</li></ul>',
    },
    {
      question: 'Application is slow with many tasks',
      answer: '<p>Performance may be affected with thousands of tasks:</p><ul><li>Use filters to reduce displayed tasks</li><li>Consider splitting large datasets into smaller files</li><li>Clear browser cache and restart the application</li></ul>',
    },
  ])

  const downloadExample = () => {
    const exampleData = [
      {
        id: 'EXAMPLE-001',
        summary: 'Set up project repository',
        description: 'Initialize a new project repository with proper folder structure, README, and initial configuration files.',
        linked_tasks: [],
        epic: 'Project Setup',
        priority: 'High',
        estimated_effort: 'Small',
        type: 'Task',
      },
      {
        id: 'EXAMPLE-002',
        summary: 'Design user interface mockups',
        description: 'Create wireframes and mockups for the main user interface screens including dashboard, forms, and navigation.',
        linked_tasks: ['EXAMPLE-001'],
        epic: 'UI/UX Design',
        priority: 'Medium',
        estimated_effort: 'Large',
        type: 'Story',
      },
      {
        id: 'EXAMPLE-003',
        summary: 'Implement user authentication',
        description: 'Build user registration, login, and authentication system with proper security measures.',
        linked_tasks: ['EXAMPLE-002'],
        epic: 'Authentication',
        priority: 'High',
        estimated_effort: 'Medium',
        type: 'Story',
      },
    ]

    const blob = new Blob([JSON.stringify(exampleData, null, 2)], {
      type: 'application/json',
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'example-tasks.json'
    document.body.append(link)
    link.click()
    link.remove()
    URL.revokeObjectURL(url)
  }
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.v-code pre {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
}
</style>
