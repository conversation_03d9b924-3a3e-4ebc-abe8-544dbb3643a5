<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="4">
        <v-card class="elevation-12">
          <v-toolbar color="primary" dark flat>
            <v-toolbar-title>Login to Track Tasks</v-toolbar-title>
          </v-toolbar>
          <v-card-text>
            <v-form ref="form" v-model="valid" lazy-validation>
              <v-text-field
                v-model="email"
                :rules="emailRules"
                label="Email"
                type="email"
                prepend-icon="mdi-email"
                required
                :disabled="loading"
              ></v-text-field>
              
              <v-text-field
                v-model="password"
                :rules="passwordRules"
                label="Password"
                :type="showPassword ? 'text' : 'password'"
                :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append="showPassword = !showPassword"
                prepend-icon="mdi-lock"
                required
                :disabled="loading"
              ></v-text-field>
              
              <v-checkbox
                v-model="rememberMe"
                label="Remember me"
                :disabled="loading"
              ></v-checkbox>
              
              <v-alert
                v-if="errorMessage"
                type="error"
                dismissible
                @click:close="errorMessage = ''"
              >
                {{ errorMessage }}
              </v-alert>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-btn
              text
              color="primary"
              @click="goToRegister"
              :disabled="loading"
            >
              Sign Up
            </v-btn>
            <v-btn
              text
              color="primary"
              @click="goToForgotPassword"
              :disabled="loading"
            >
              Forgot Password?
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              @click="login"
              :disabled="!valid || loading"
              :loading="loading"
            >
              Login
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// Form state
const valid = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)

// Form data
const email = ref('')
const password = ref('')

// Use auth store state
const loading = computed(() => authStore.isLoading)
const errorMessage = computed(() => authStore.authError)

// Form validation rules
const emailRules = [
  v => !!v || 'Email is required',
  v => /.+@.+\..+/.test(v) || 'Email must be valid'
]

const passwordRules = [
  v => !!v || 'Password is required',
  v => v.length >= 8 || 'Password must be at least 8 characters'
]

// Methods
const login = async () => {
  if (!valid.value) return
  
  loading.value = true
  errorMessage.value = ''
  
  try {
    await authStore.login(email.value, password.value, rememberMe.value)
    
    // Redirect to intended page or home
    const redirectTo = router.currentRoute.value.query.redirect || '/'
    router.push(redirectTo)
  } catch (error) {
    errorMessage.value = error.message || 'Login failed. Please try again.'
  } finally {
    loading.value = false
  }
}

const goToRegister = () => {
  router.push('/register')
}

const goToForgotPassword = () => {
  router.push('/forgot-password')
}

// Redirect if already logged in
if (authStore.isAuthenticated) {
  router.push('/')
}
</script>

<style scoped>
.fill-height {
  min-height: 100vh;
}
</style>
