<template>
  <div class="sessions-page">
    <!-- Page Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">
          Active Sessions
        </h1>
        <p class="text-body-1 text-medium-emphasis">
          Manage your active sessions across all devices and browsers
        </p>
      </div>
      
      <v-btn
        color="primary"
        variant="elevated"
        @click="refreshSessions"
        :loading="loading"
      >
        <v-icon start>mdi-refresh</v-icon>
        Refresh
      </v-btn>
    </div>

    <!-- Session Statistics -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card variant="tonal" color="primary">
          <v-card-text class="text-center">
            <v-icon size="32" class="mb-2">mdi-account-circle</v-icon>
            <div class="text-h5 font-weight-bold">{{ totalSessions }}</div>
            <div class="text-body-2">Total Sessions</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card variant="tonal" color="success">
          <v-card-text class="text-center">
            <v-icon size="32" class="mb-2">mdi-check-circle</v-icon>
            <div class="text-h5 font-weight-bold">{{ activeSessions.length }}</div>
            <div class="text-body-2">Active Sessions</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card variant="tonal" color="info">
          <v-card-text class="text-center">
            <v-icon size="32" class="mb-2">mdi-devices</v-icon>
            <div class="text-h5 font-weight-bold">{{ otherSessions.length }}</div>
            <div class="text-body-2">Other Devices</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card variant="tonal" color="warning">
          <v-card-text class="text-center">
            <v-icon size="32" class="mb-2">mdi-security</v-icon>
            <div class="text-h5 font-weight-bold">{{ hasMultipleSessions ? 'Yes' : 'No' }}</div>
            <div class="text-body-2">Multiple Sessions</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Security Alert -->
    <v-alert
      v-if="hasMultipleSessions"
      type="warning"
      variant="tonal"
      class="mb-6"
    >
      <template #prepend>
        <v-icon>mdi-shield-alert</v-icon>
      </template>
      
      <div class="d-flex align-center justify-space-between">
        <div>
          <strong>Multiple active sessions detected</strong><br>
          <span class="text-body-2">
            You have {{ otherSessions.length }} other active session{{ otherSessions.length > 1 ? 's' : '' }}.
            If you don't recognize any of these sessions, terminate them immediately.
          </span>
        </div>
        
        <v-btn
          color="warning"
          variant="elevated"
          @click="confirmTerminateAll"
          :disabled="otherSessions.length === 0"
        >
          <v-icon start>mdi-logout</v-icon>
          Terminate All Others
        </v-btn>
      </div>
    </v-alert>

    <!-- Error Alert -->
    <v-alert
      v-if="error"
      type="error"
      variant="tonal"
      class="mb-6"
      closable
      @click:close="clearError"
    >
      <template #prepend>
        <v-icon>mdi-alert-circle</v-icon>
      </template>
      
      <strong>Error loading sessions</strong><br>
      {{ error }}
    </v-alert>

    <!-- Loading State -->
    <div v-if="loading && sessions.length === 0" class="text-center py-12">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
        class="mb-4"
      />
      <p class="text-body-1">Loading sessions...</p>
    </div>

    <!-- Sessions List -->
    <div v-else-if="sessions.length > 0">
      <!-- Current Session -->
      <div v-if="currentSession" class="mb-6">
        <h2 class="text-h6 font-weight-medium mb-3">
          <v-icon class="mr-2">mdi-account-check</v-icon>
          Current Session
        </h2>
        
        <SessionCard
          :session="currentSession"
          @refresh="refreshCurrentSession"
        />
      </div>

      <!-- Other Sessions -->
      <div v-if="otherSessions.length > 0">
        <h2 class="text-h6 font-weight-medium mb-3">
          <v-icon class="mr-2">mdi-devices</v-icon>
          Other Sessions ({{ otherSessions.length }})
        </h2>
        
        <v-row>
          <v-col
            v-for="session in otherSessions"
            :key="session.id"
            cols="12"
            md="6"
            lg="4"
          >
            <SessionCard
              :session="session"
              @terminate="terminateSession"
            />
          </v-col>
        </v-row>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="text-center py-12">
      <v-icon size="64" color="grey-lighten-1" class="mb-4">
        mdi-account-off
      </v-icon>
      <h3 class="text-h6 mb-2">No Active Sessions</h3>
      <p class="text-body-2 text-medium-emphasis mb-4">
        You don't have any active sessions at the moment.
      </p>
      <v-btn
        color="primary"
        variant="elevated"
        @click="refreshSessions"
      >
        <v-icon start>mdi-refresh</v-icon>
        Refresh Sessions
      </v-btn>
    </div>

    <!-- Terminate All Confirmation Dialog -->
    <v-dialog
      v-model="showTerminateAllDialog"
      max-width="500"
    >
      <v-card>
        <v-card-title class="text-h6 text-error">
          <v-icon color="error" class="mr-2">mdi-alert-circle</v-icon>
          Terminate All Other Sessions
        </v-card-title>
        
        <v-card-text>
          <p class="mb-3">
            Are you sure you want to terminate all other active sessions?
          </p>
          
          <v-alert
            type="warning"
            variant="tonal"
            class="mb-3"
          >
            <div class="text-body-2">
              This will log you out from <strong>{{ otherSessions.length }}</strong> 
              other device{{ otherSessions.length > 1 ? 's' : '' }}:
            </div>
            <ul class="mt-2">
              <li v-for="session in otherSessions.slice(0, 3)" :key="session.id">
                {{ formatSession(session).deviceType }} - {{ formatSession(session).browserName }}
              </li>
              <li v-if="otherSessions.length > 3">
                ... and {{ otherSessions.length - 3 }} more
              </li>
            </ul>
          </v-alert>
          
          <p class="text-body-2 text-medium-emphasis">
            This action cannot be undone. You will need to log in again on those devices.
          </p>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="text"
            @click="showTerminateAllDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="elevated"
            @click="terminateAllOtherSessions"
            :loading="terminatingAll"
          >
            <v-icon start>mdi-logout</v-icon>
            Terminate All
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useSessionManagement } from '../composables/useSessionManagement.js'
import SessionCard from '../components/SessionCard.vue'

/**
 * Page metadata
 */
definePageMeta({
  title: 'Active Sessions',
  requiresAuth: true
})

/**
 * Composables
 */
const {
  sessions,
  loading,
  error,
  activeSessions,
  currentSession,
  otherSessions,
  totalSessions,
  hasMultipleSessions,
  fetchSessions,
  terminateSession: terminateSessionApi,
  terminateAllOtherSessions: terminateAllApi,
  refreshCurrentSession,
  formatSession,
  clearError
} = useSessionManagement()

/**
 * Reactive state
 */
const showTerminateAllDialog = ref(false)
const terminatingAll = ref(false)

/**
 * Methods
 */
const refreshSessions = async () => {
  await fetchSessions()
}

const terminateSession = async (sessionId) => {
  try {
    await terminateSessionApi(sessionId)
    // Success feedback is handled by the SessionCard component
  } catch (error) {
    console.error('Failed to terminate session:', error)
  }
}

const confirmTerminateAll = () => {
  showTerminateAllDialog.value = true
}

const terminateAllOtherSessions = async () => {
  terminatingAll.value = true
  
  try {
    await terminateAllApi()
    showTerminateAllDialog.value = false
  } catch (error) {
    console.error('Failed to terminate all sessions:', error)
  } finally {
    terminatingAll.value = false
  }
}
</script>

<style scoped>
.sessions-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

@media (max-width: 768px) {
  .sessions-page {
    padding: 16px;
  }
}
</style>
