<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">Database Migration</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Migrate your database to support the new project-based task organization
            </p>
          </div>
          <v-btn
            color="primary"
            prepend-icon="mdi-arrow-left"
            variant="outlined"
            @click="$router.push('/')"
          >
            Back to Tasks
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Migration Manager -->
    <v-row class="mb-6">
      <v-col>
        <MigrationManager />
      </v-col>
    </v-row>

    <!-- System Information -->
    <v-row>
      <v-col cols="12" md="6">
        <v-card elevation="2">
          <v-card-title>
            <v-icon class="mr-2" color="info">mdi-information</v-icon>
            System Information
          </v-card-title>
          <v-card-text>
            <v-list density="compact">
              <v-list-item>
                <template #prepend>
                  <v-icon color="primary">mdi-database</v-icon>
                </template>
                <v-list-item-title>Database Status</v-list-item-title>
                <v-list-item-subtitle>
                  {{ systemInfo.databaseStatus }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-folder-multiple</v-icon>
                </template>
                <v-list-item-title>Total Projects</v-list-item-title>
                <v-list-item-subtitle>
                  {{ systemInfo.totalProjects }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <template #prepend>
                  <v-icon color="warning">mdi-format-list-checks</v-icon>
                </template>
                <v-list-item-title>Total Tasks</v-list-item-title>
                <v-list-item-subtitle>
                  {{ systemInfo.totalTasks }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <template #prepend>
                  <v-icon color="info">mdi-link</v-icon>
                </template>
                <v-list-item-title>Tasks with Projects</v-list-item-title>
                <v-list-item-subtitle>
                  {{ systemInfo.tasksWithProjects }} / {{ systemInfo.totalTasks }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
          
          <v-card-actions>
            <v-btn
              color="primary"
              prepend-icon="mdi-refresh"
              variant="outlined"
              @click="refreshSystemInfo"
            >
              Refresh
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6">
        <v-card elevation="2">
          <v-card-title>
            <v-icon class="mr-2" color="warning">mdi-alert-circle</v-icon>
            Important Notes
          </v-card-title>
          <v-card-text>
            <v-alert
              color="info"
              icon="mdi-information"
              variant="tonal"
              class="mb-4"
            >
              <strong>Before Migration:</strong><br>
              Make sure to backup your data if you have important tasks stored.
            </v-alert>
            
            <v-alert
              color="warning"
              icon="mdi-alert"
              variant="tonal"
              class="mb-4"
            >
              <strong>During Migration:</strong><br>
              Do not close this page or refresh the browser while migration is running.
            </v-alert>
            
            <v-alert
              color="success"
              icon="mdi-check-circle"
              variant="tonal"
            >
              <strong>After Migration:</strong><br>
              All your tasks will be organized under projects. You can create new projects and move tasks between them.
            </v-alert>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import MigrationManager from '@/components/migration/MigrationManager.vue'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'

// Router
const router = useRouter()

// Stores
const projectsStore = useProjectsStore()
const tasksStore = useTasksStore()

// Reactive data
const systemInfo = ref({
  databaseStatus: 'Checking...',
  totalProjects: 0,
  totalTasks: 0,
  tasksWithProjects: 0,
})

// Methods
const refreshSystemInfo = async () => {
  try {
    systemInfo.value.databaseStatus = 'Loading...'
    
    // Fetch current data
    await Promise.all([
      projectsStore.fetchProjects(),
      tasksStore.fetchTasks(),
    ])
    
    // Update system info
    systemInfo.value.totalProjects = projectsStore.projects.length
    systemInfo.value.totalTasks = tasksStore.tasks.length
    systemInfo.value.tasksWithProjects = tasksStore.tasks.filter(task => task.project_id).length
    systemInfo.value.databaseStatus = 'Connected'
    
  } catch (error) {
    console.error('Failed to refresh system info:', error)
    systemInfo.value.databaseStatus = 'Error'
  }
}

// Lifecycle
onMounted(() => {
  refreshSystemInfo()
})
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}
</style>
