/**
 * Database Initializer for PocketBase
 * Handles database schema setup and initial data creation
 */
import databaseService from '@common/services/databaseService.js'

class DatabaseInitializer {
  constructor() {
    this.isInitialized = false
  }

  /**
   * Initialize the database schema and create default project if needed
   */
  async initialize() {
    if (this.isInitialized) {
      return
    }

    try {
      console.log('Initializing database schema...')

      // Check if we need to create a default project
      await this.ensureDefaultProject()

      // Run migration for existing tasks if needed
      await this.migrateExistingTasks()

      this.isInitialized = true
      console.log('Database initialization completed successfully')
    } catch (error) {
      console.error('Database initialization failed:', error)
      throw error
    }
  }

  /**
   * Ensure there's at least one project in the database
   * Creates a default project if none exist
   */
  async ensureDefaultProject() {
    try {
      const projects = await databaseService.getAllProjects()

      if (projects.length === 0) {
        console.log('No projects found, creating default project...')

        const defaultProject = {
          name: 'Default Project',
          description: 'Default project for imported tasks. You can rename this or create new projects.',
        }

        const createdProject = await databaseService.addProject(defaultProject)
        console.log('Default project created:', createdProject.name)

        return createdProject
      }

      return projects[0] // Return first project as default
    } catch (error) {
      console.error('Failed to ensure default project:', error)
      throw error
    }
  }

  /**
   * Get the default project ID for task imports
   * @returns {string} - Default project ID
   */
  async getDefaultProjectId() {
    const projects = await databaseService.getAllProjects()

    if (projects.length === 0) {
      const defaultProject = await this.ensureDefaultProject()
      return defaultProject.id
    }

    return projects[0].id
  }

  /**
   * Migrate existing tasks to use projects
   * This method handles the transition from the old schema to the new one
   */
  async migrateExistingTasks() {
    try {
      console.log('Checking for tasks that need project assignment...')

      // Get all tasks without project_id (this will fail if schema hasn't been updated)
      const tasks = await databaseService.getAllTasks()

      // Filter tasks that don't have project_id
      const tasksWithoutProject = tasks.filter(task => !task.project_id)

      if (tasksWithoutProject.length === 0) {
        console.log('All tasks already have project assignments')
        return
      }

      console.log(`Found ${tasksWithoutProject.length} tasks without project assignment`)

      // Get or create default project
      const defaultProjectId = await this.getDefaultProjectId()

      // Update tasks to include project_id
      const updatePromises = tasksWithoutProject.map(task =>
        databaseService.updateTask(task.id, { project_id: defaultProjectId })
      )

      await Promise.all(updatePromises)
      console.log(`Successfully assigned ${tasksWithoutProject.length} tasks to default project`)

    } catch (error) {
      // This is expected if the schema hasn't been updated yet
      console.log('Task migration not needed or schema not yet updated:', error.message)
    }
  }

  /**
   * Clear all data and reinitialize (for development/testing)
   */
  async resetDatabase() {
    try {
      console.log('Resetting database...')

      // Clear all data
      await databaseService.clearAllData()

      // Reinitialize
      this.isInitialized = false
      await this.initialize()

      console.log('Database reset completed')
    } catch (error) {
      console.error('Database reset failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export default new DatabaseInitializer()
