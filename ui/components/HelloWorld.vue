<template>
  <v-container class="fill-height" max-width="900">
    <div>
      <v-img
        class="mb-4"
        height="150"
        src="@/assets/logo.png"
      />

      <div class="mb-8 text-center">
        <div class="text-body-2 font-weight-light mb-n1">Welcome to</div>
        <h1 class="text-h2 font-weight-bold">Track Tasks</h1>
        <p class="text-h6 text-medium-emphasis mt-2">Project Task Management System</p>
      </div>

      <v-row>
        <v-col cols="12">
          <v-card
            class="py-4"
            color="primary"
            prepend-icon="mdi-file-upload"
            rounded="lg"
            variant="elevated"
            @click="$router.push('/tasks')"
            style="cursor: pointer;"
          >
            <template #title>
              <h2 class="text-h5 font-weight-bold text-white">
                Task Manager
              </h2>
            </template>

            <template #subtitle>
              <div class="text-subtitle-1 text-white">
                Upload JSON files containing your project tasks and manage them in a local database. 
                Upload your <v-kbd>backlog.json</v-kbd> file to get started.
              </div>
            </template>

            <v-card-actions>
              <v-spacer />
              <v-btn 
                color="white" 
                variant="elevated"
                append-icon="mdi-arrow-right"
              >
                Get Started
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>

        <!-- Track Tasks Feature Cards -->
        <v-col v-for="feature in features" :key="feature.title" cols="12" md="6">
          <v-card
            class="py-4 h-100"
            color="surface-variant"
            :prepend-icon="feature.icon"
            rounded="lg"
            :subtitle="feature.subtitle"
            :title="feature.title"
            variant="tonal"
            @click="feature.action && feature.action()"
            :style="feature.action ? 'cursor: pointer;' : ''"
          >
            <v-card-actions v-if="feature.action">
              <v-spacer />
              <v-btn variant="text" color="primary">
                {{ feature.buttonText }}
                <v-icon end>mdi-arrow-right</v-icon>
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>

      <!-- Task Statistics -->
      <TaskStatistics />
    </div>
  </v-container>
</template>

<script setup>
import { useRouter } from 'vue-router'
import TaskStatistics from '@/components/TaskStatistics.vue'

const $router = useRouter()

const features = [
  {
    icon: 'mdi-database-import',
    title: 'JSON File Import',
    subtitle: 'Upload and import task data from JSON files with validation and error handling.',
    buttonText: 'Upload Tasks',
    action: () => $router.push('/tasks')
  },
  {
    icon: 'mdi-filter-variant',
    title: 'Advanced Filtering',
    subtitle: 'Filter and search tasks by type, priority, status, epic, and custom criteria.',
    buttonText: 'View Tasks',
    action: () => $router.push('/tasks')
  },
  {
    icon: 'mdi-database-outline',
    title: 'Local Storage',
    subtitle: 'Tasks are stored locally in your browser using IndexedDB for offline access.',
  },
  {
    icon: 'mdi-chart-line',
    title: 'Task Analytics',
    subtitle: 'View statistics and insights about your project tasks and completion rates.',
  }
]
</script>
