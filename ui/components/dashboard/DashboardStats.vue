<template>
  <v-row v-if="tasksStore.tasks.length > 0" class="mb-6">
    <v-col cols="12" md="3">
      <v-card color="primary" variant="elevated">
        <v-card-text class="text-center">
          <v-icon class="mb-2" size="48">mdi-format-list-checks</v-icon>
          <div class="text-h4 font-weight-bold">{{ tasksStore.statistics.total }}</div>
          <div class="text-subtitle-1">Total Tasks</div>
        </v-card-text>
      </v-card>
    </v-col>

    <v-col cols="12" md="3">
      <v-card color="success" variant="elevated">
        <v-card-text class="text-center">
          <v-icon class="mb-2" size="48">mdi-check-circle</v-icon>
          <div class="text-h4 font-weight-bold">
            {{ tasksStore.tasksByStatus.Done?.length || 0 }}
          </div>
          <div class="text-subtitle-1">Completed</div>
        </v-card-text>
      </v-card>
    </v-col>

    <v-col cols="12" md="3">
      <v-card color="warning" variant="elevated">
        <v-card-text class="text-center">
          <v-icon class="mb-2" size="48">mdi-clock-outline</v-icon>
          <div class="text-h4 font-weight-bold">
            {{ tasksStore.tasksByStatus['In Progress']?.length || 0 }}
          </div>
          <div class="text-subtitle-1">In Progress</div>
        </v-card-text>
      </v-card>
    </v-col>

    <v-col cols="12" md="3">
      <v-card color="info" variant="elevated">
        <v-card-text class="text-center">
          <v-icon class="mb-2" size="48">mdi-playlist-plus</v-icon>
          <div class="text-h4 font-weight-bold">
            {{ tasksStore.tasksByStatus.Backlog?.length || 0 }}
          </div>
          <div class="text-subtitle-1">Backlog</div>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>

<script setup>
import { useTasksStore } from '@/stores/tasks';

const tasksStore = useTasksStore();
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}
</style>
