<template>
  <v-dialog
    v-model="showDialog"
    persistent
    max-width="500"
    :z-index="9999"
  >
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon color="warning" class="mr-2">
          mdi-clock-alert-outline
        </v-icon>
        Session Timeout Warning
      </v-card-title>

      <v-card-text>
        <div class="text-center mb-4">
          <v-icon
            size="64"
            color="warning"
            class="mb-2"
          >
            mdi-timer-sand
          </v-icon>
          
          <h3 class="text-h5 mb-2">
            Your session will expire in
          </h3>
          
          <div class="text-h3 font-weight-bold text-warning mb-3">
            {{ formattedTimeRemaining }}
          </div>
          
          <p class="text-body-1 text-medium-emphasis">
            You will be automatically logged out for security reasons.
            Click "Stay Logged In" to extend your session.
          </p>
        </div>

        <v-progress-linear
          :model-value="progressPercentage"
          color="warning"
          height="8"
          rounded
          class="mb-4"
        />

        <v-alert
          type="info"
          variant="tonal"
          class="mb-4"
        >
          <template #prepend>
            <v-icon>mdi-information-outline</v-icon>
          </template>
          
          <div class="text-body-2">
            <strong>Security Notice:</strong> Sessions automatically expire after periods of inactivity to protect your account.
          </div>
        </v-alert>
      </v-card-text>

      <v-card-actions class="px-6 pb-6">
        <v-btn
          color="error"
          variant="outlined"
          @click="logoutNow"
          :disabled="extending"
        >
          <v-icon start>mdi-logout</v-icon>
          Logout Now
        </v-btn>

        <v-spacer />

        <v-btn
          color="primary"
          variant="elevated"
          @click="extendSession"
          :loading="extending"
          :disabled="timeRemaining <= 0"
        >
          <v-icon start>mdi-refresh</v-icon>
          Stay Logged In
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <!-- Session Expired Dialog -->
  <v-dialog
    v-model="showExpiredDialog"
    persistent
    max-width="400"
  >
    <v-card>
      <v-card-title class="d-flex align-center text-error">
        <v-icon color="error" class="mr-2">
          mdi-clock-remove-outline
        </v-icon>
        Session Expired
      </v-card-title>

      <v-card-text class="text-center">
        <v-icon
          size="64"
          color="error"
          class="mb-3"
        >
          mdi-account-clock
        </v-icon>
        
        <h4 class="text-h6 mb-3">
          Your session has expired
        </h4>
        
        <p class="text-body-2 text-medium-emphasis">
          For your security, you have been automatically logged out due to inactivity.
          Please log in again to continue.
        </p>
      </v-card-text>

      <v-card-actions class="justify-center pb-6">
        <v-btn
          color="primary"
          variant="elevated"
          @click="redirectToLogin"
        >
          <v-icon start>mdi-login</v-icon>
          Go to Login
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

/**
 * Props
 */
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  timeRemaining: {
    type: Number,
    default: 0
  },
  formattedTime: {
    type: String,
    default: '0:00'
  },
  totalWarningTime: {
    type: Number,
    default: 5 * 60 * 1000 // 5 minutes
  }
})

/**
 * Emits
 */
const emit = defineEmits([
  'extend-session',
  'logout-now',
  'session-expired'
])

/**
 * Composables
 */
const router = useRouter()
const authStore = useAuthStore()

/**
 * Reactive state
 */
const showDialog = ref(false)
const showExpiredDialog = ref(false)
const extending = ref(false)

/**
 * Computed properties
 */
const formattedTimeRemaining = computed(() => {
  return props.formattedTime
})

const progressPercentage = computed(() => {
  if (props.totalWarningTime <= 0) return 0
  return Math.max(0, (props.timeRemaining / props.totalWarningTime) * 100)
})

/**
 * Watch for prop changes
 */
watch(() => props.show, (newValue) => {
  showDialog.value = newValue
  
  if (newValue) {
    showExpiredDialog.value = false
  }
})

watch(() => props.timeRemaining, (newValue) => {
  if (newValue <= 0 && showDialog.value) {
    handleSessionExpired()
  }
})

/**
 * Methods
 */
const extendSession = async () => {
  extending.value = true
  
  try {
    emit('extend-session')
    
    // Wait a moment for the extension to process
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showDialog.value = false
  } catch (error) {
    console.error('Failed to extend session:', error)
    
    // Show error message
    authStore.setError('Failed to extend session. Please log in again.')
  } finally {
    extending.value = false
  }
}

const logoutNow = () => {
  showDialog.value = false
  emit('logout-now')
}

const handleSessionExpired = () => {
  showDialog.value = false
  showExpiredDialog.value = true
  emit('session-expired')
}

const redirectToLogin = () => {
  showExpiredDialog.value = false
  router.push({
    name: 'login',
    query: { reason: 'expired' }
  })
}

/**
 * Keyboard shortcuts
 */
const handleKeydown = (event) => {
  if (!showDialog.value) return
  
  // Enter key extends session
  if (event.key === 'Enter' && !extending.value) {
    event.preventDefault()
    extendSession()
  }
  
  // Escape key logs out
  if (event.key === 'Escape') {
    event.preventDefault()
    logoutNow()
  }
}

/**
 * Lifecycle hooks
 */
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.v-dialog {
  /* Ensure dialog appears above all other content */
  z-index: 9999 !important;
}

.text-h3 {
  font-family: 'Roboto Mono', monospace;
  letter-spacing: 0.1em;
}

.v-progress-linear {
  border-radius: 4px;
}

/* Pulse animation for warning state */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.text-warning {
  animation: pulse 2s infinite;
}

/* Accessibility improvements */
.v-card-title {
  font-weight: 600;
}

.v-btn {
  min-height: 44px; /* Ensure touch-friendly button size */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-warning {
    color: #ff6f00 !important;
  }
  
  .v-progress-linear {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .text-warning {
    animation: none;
  }
}
</style>
