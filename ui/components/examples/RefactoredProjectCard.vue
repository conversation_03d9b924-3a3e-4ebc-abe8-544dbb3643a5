<!--
  This is an example showing how ProjectCard.vue could be refactored
  to use the new reusable utilities we've created
-->

<template>
  <v-card
    :class="{ 'project-card--selected': selected }"
    class="project-card"
    elevation="2"
    hover
    @click="$emit('select', project)"
  >
    <v-card-title class="d-flex align-center">
      <v-avatar :color="selected ? 'primary' : 'grey'" class="mr-3" size="40">
        <v-icon :color="selected ? 'white' : 'grey-darken-1'">
          mdi-folder
        </v-icon>
      </v-avatar>
      <div class="flex-1-1">
        <div class="text-h6">{{ project.name }}</div>
        <div v-if="project.description" class="text-body-2 text-medium-emphasis">
          {{ truncateText(project.description, 60) }}
        </div>
      </div>
    </v-card-title>

    <v-card-text>
      <!-- Project Statistics -->
      <v-row class="mb-3">
        <v-col cols="6">
          <div class="text-center">
            <div class="text-h6 text-primary">{{ stats.totalTasks }}</div>
            <div class="text-caption text-medium-emphasis">Total Tasks</div>
          </div>
        </v-col>
        <v-col cols="6">
          <div class="text-center">
            <div class="text-h6 text-success">{{ stats.completionRate }}%</div>
            <div class="text-caption text-medium-emphasis">Complete</div>
          </div>
        </v-col>
      </v-row>

      <!-- Progress Bar -->
      <v-progress-linear
        :model-value="stats.completionRate"
        :color="getProgressColor(stats.completionRate)"
        height="6"
        rounded
      />

      <!-- Task Status Breakdown -->
      <div class="mt-3">
        <v-row dense>
          <v-col cols="4">
            <v-chip
              color="info"
              size="small"
              variant="tonal"
            >
              {{ stats.backlogTasks }} Backlog
            </v-chip>
          </v-col>
          <v-col cols="4">
            <v-chip
              color="warning"
              size="small"
              variant="tonal"
            >
              {{ stats.inProgressTasks }} Active
            </v-chip>
          </v-col>
          <v-col cols="4">
            <v-chip
              color="success"
              size="small"
              variant="tonal"
            >
              {{ stats.completedTasks }} Done
            </v-chip>
          </v-col>
        </v-row>
      </div>
    </v-card-text>

    <v-card-actions v-if="showActions">
      <v-spacer />
      <v-btn
        icon="mdi-pencil"
        size="small"
        variant="text"
        @click.stop="$emit('edit', project)"
      />
      <v-btn
        :disabled="stats.totalTasks > 0"
        icon="mdi-delete"
        size="small"
        variant="text"
        @click.stop="$emit('delete', project)"
      />
    </v-card-actions>

    <!-- Notification Snackbar -->
    <v-snackbar
      v-model="notification.show"
      :color="notification.color"
      :timeout="notification.timeout"
    >
      {{ notification.message }}
      <template #actions>
        <v-btn
          color="white"
          variant="text"
          @click="notification.hide"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'
import { useTasksStore } from '@/stores/tasks'

// Import our new reusable utilities
import { truncateText, getProgressColor } from '@/utils/taskDisplayUtils'
import { generateProjectStatistics } from '@/utils/statisticsUtils'
import { useNotification } from '@/composables/useNotification'

const props = defineProps({
  project: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  showActions: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['select', 'edit', 'delete'])

// Store
const tasksStore = useTasksStore()

// Use the notification composable
const notification = useNotification()

// Computed
const stats = computed(() => {
  // Get tasks for this project
  const projectTasks = tasksStore.filterTasksByProject(props.project.id)
  
  // Use the new statistics utility instead of the store method
  return generateProjectStatistics(projectTasks)
})

// Example of how notifications could be used
const handleEdit = () => {
  notification.showInfo('Opening project editor...')
  $emit('edit', props.project)
}

const handleDelete = () => {
  if (stats.value.totalTasks > 0) {
    notification.showWarning('Cannot delete project with tasks')
    return
  }
  notification.showInfo('Deleting project...')
  $emit('delete', props.project)
}
</script>

<style scoped>
.project-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.project-card--selected {
  border-color: rgb(var(--v-theme-primary));
  background-color: rgba(var(--v-theme-primary), 0.05);
}

.text-wrap {
  word-break: break-word;
  white-space: normal;
}
</style>
