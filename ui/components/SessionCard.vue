<template>
  <v-card
    :class="[
      'session-card',
      { 'current-session': session.isCurrent }
    ]"
    :variant="session.isCurrent ? 'elevated' : 'outlined'"
    :color="session.isCurrent ? 'primary' : undefined"
  >
    <v-card-text>
      <div class="d-flex align-start">
        <!-- Device Icon -->
        <v-avatar
          :color="session.isCurrent ? 'primary' : 'grey-lighten-1'"
          size="48"
          class="mr-4"
        >
          <v-icon
            :color="session.isCurrent ? 'white' : 'grey-darken-1'"
            size="24"
          >
            {{ deviceIcon }}
          </v-icon>
        </v-avatar>

        <!-- Session Details -->
        <div class="flex-grow-1">
          <div class="d-flex align-center mb-1">
            <h4 class="text-subtitle-1 font-weight-medium">
              {{ deviceName }}
            </h4>
            
            <v-chip
              v-if="session.isCurrent"
              color="success"
              size="small"
              class="ml-2"
            >
              <v-icon start size="12">mdi-check-circle</v-icon>
              Current
            </v-chip>
          </div>

          <div class="text-body-2 text-medium-emphasis mb-2">
            <div class="d-flex align-center mb-1">
              <v-icon size="16" class="mr-1">mdi-web</v-icon>
              {{ browserName }}
            </div>
            
            <div class="d-flex align-center mb-1">
              <v-icon size="16" class="mr-1">mdi-map-marker</v-icon>
              {{ session.ip || 'Unknown location' }}
            </div>
            
            <div class="d-flex align-center">
              <v-icon size="16" class="mr-1">mdi-clock-outline</v-icon>
              Last active {{ formatLastActivity }}
            </div>
          </div>

          <!-- Session Status -->
          <div class="d-flex align-center">
            <v-chip
              :color="statusColor"
              size="small"
              variant="tonal"
            >
              <v-icon start size="12">{{ statusIcon }}</v-icon>
              {{ statusText }}
            </v-chip>
            
            <v-spacer />
            
            <!-- Actions -->
            <div class="d-flex gap-2">
              <v-btn
                v-if="!session.isCurrent"
                color="error"
                variant="outlined"
                size="small"
                @click="confirmTerminate"
                :loading="terminating"
              >
                <v-icon start size="16">mdi-logout</v-icon>
                Terminate
              </v-btn>
              
              <v-menu v-if="session.isCurrent">
                <template #activator="{ props }">
                  <v-btn
                    variant="outlined"
                    size="small"
                    v-bind="props"
                  >
                    <v-icon start size="16">mdi-dots-vertical</v-icon>
                    Options
                  </v-btn>
                </template>
                
                <v-list>
                  <v-list-item @click="refreshSession">
                    <template #prepend>
                      <v-icon>mdi-refresh</v-icon>
                    </template>
                    <v-list-item-title>Refresh Session</v-list-item-title>
                  </v-list-item>
                  
                  <v-list-item @click="viewDetails">
                    <template #prepend>
                      <v-icon>mdi-information-outline</v-icon>
                    </template>
                    <v-list-item-title>View Details</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </div>
        </div>
      </div>
    </v-card-text>

    <!-- Confirmation Dialog -->
    <v-dialog
      v-model="showConfirmDialog"
      max-width="400"
    >
      <v-card>
        <v-card-title class="text-h6">
          Terminate Session
        </v-card-title>
        
        <v-card-text>
          <p class="mb-3">
            Are you sure you want to terminate this session?
          </p>
          
          <v-alert
            type="warning"
            variant="tonal"
            class="mb-3"
          >
            <div class="text-body-2">
              <strong>{{ deviceName }}</strong><br>
              {{ browserName }}<br>
              IP: {{ session.ip }}
            </div>
          </v-alert>
          
          <p class="text-body-2 text-medium-emphasis">
            This action cannot be undone. The user will be logged out immediately.
          </p>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="text"
            @click="showConfirmDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="elevated"
            @click="terminateSession"
            :loading="terminating"
          >
            Terminate
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Session Details Dialog -->
    <v-dialog
      v-model="showDetailsDialog"
      max-width="600"
    >
      <v-card>
        <v-card-title class="text-h6">
          Session Details
        </v-card-title>
        
        <v-card-text>
          <v-list>
            <v-list-item>
              <template #prepend>
                <v-icon>mdi-identifier</v-icon>
              </template>
              <v-list-item-title>Session ID</v-list-item-title>
              <v-list-item-subtitle class="font-mono">
                {{ session.id }}
              </v-list-item-subtitle>
            </v-list-item>
            
            <v-list-item>
              <template #prepend>
                <v-icon>{{ deviceIcon }}</v-icon>
              </template>
              <v-list-item-title>Device</v-list-item-title>
              <v-list-item-subtitle>{{ deviceName }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-list-item>
              <template #prepend>
                <v-icon>mdi-web</v-icon>
              </template>
              <v-list-item-title>Browser</v-list-item-title>
              <v-list-item-subtitle>{{ browserName }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-list-item>
              <template #prepend>
                <v-icon>mdi-map-marker</v-icon>
              </template>
              <v-list-item-title>IP Address</v-list-item-title>
              <v-list-item-subtitle>{{ session.ip || 'Unknown' }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-list-item>
              <template #prepend>
                <v-icon>mdi-calendar-plus</v-icon>
              </template>
              <v-list-item-title>Created</v-list-item-title>
              <v-list-item-subtitle>{{ formatDate(session.createdAt) }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-list-item>
              <template #prepend>
                <v-icon>mdi-clock-outline</v-icon>
              </template>
              <v-list-item-title>Last Activity</v-list-item-title>
              <v-list-item-subtitle>{{ formatDate(session.lastActivity) }}</v-list-item-subtitle>
            </v-list-item>
          </v-list>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="text"
            @click="showDetailsDialog = false"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTimeAgo } from '@vueuse/core'

/**
 * Props
 */
const props = defineProps({
  session: {
    type: Object,
    required: true
  }
})

/**
 * Emits
 */
const emit = defineEmits([
  'terminate',
  'refresh'
])

/**
 * Reactive state
 */
const terminating = ref(false)
const showConfirmDialog = ref(false)
const showDetailsDialog = ref(false)

/**
 * Computed properties
 */
const deviceIcon = computed(() => {
  const userAgent = props.session.userAgent?.toLowerCase() || ''
  
  if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
    return 'mdi-cellphone'
  } else if (userAgent.includes('tablet') || userAgent.includes('ipad')) {
    return 'mdi-tablet'
  } else if (userAgent.includes('mac')) {
    return 'mdi-laptop'
  } else if (userAgent.includes('windows')) {
    return 'mdi-microsoft-windows'
  } else if (userAgent.includes('linux')) {
    return 'mdi-linux'
  } else {
    return 'mdi-monitor'
  }
})

const deviceName = computed(() => {
  const userAgent = props.session.userAgent || ''
  
  if (userAgent.includes('Mobile') || userAgent.includes('Android')) {
    return 'Mobile Device'
  } else if (userAgent.includes('iPad')) {
    return 'iPad'
  } else if (userAgent.includes('iPhone')) {
    return 'iPhone'
  } else if (userAgent.includes('Mac')) {
    return 'Mac'
  } else if (userAgent.includes('Windows')) {
    return 'Windows PC'
  } else if (userAgent.includes('Linux')) {
    return 'Linux PC'
  } else {
    return 'Desktop Computer'
  }
})

const browserName = computed(() => {
  const userAgent = props.session.userAgent || ''
  
  if (userAgent.includes('Chrome')) {
    return 'Google Chrome'
  } else if (userAgent.includes('Firefox')) {
    return 'Mozilla Firefox'
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    return 'Safari'
  } else if (userAgent.includes('Edge')) {
    return 'Microsoft Edge'
  } else if (userAgent.includes('Opera')) {
    return 'Opera'
  } else {
    return 'Unknown Browser'
  }
})

const formatLastActivity = computed(() => {
  return useTimeAgo(new Date(props.session.lastActivity)).value
})

const statusColor = computed(() => {
  if (!props.session.isActive) return 'error'
  if (props.session.isCurrent) return 'success'
  return 'primary'
})

const statusIcon = computed(() => {
  if (!props.session.isActive) return 'mdi-close-circle'
  if (props.session.isCurrent) return 'mdi-check-circle'
  return 'mdi-circle'
})

const statusText = computed(() => {
  if (!props.session.isActive) return 'Inactive'
  if (props.session.isCurrent) return 'Active (Current)'
  return 'Active'
})

/**
 * Methods
 */
const confirmTerminate = () => {
  showConfirmDialog.value = true
}

const terminateSession = async () => {
  terminating.value = true
  
  try {
    await emit('terminate', props.session.id)
    showConfirmDialog.value = false
  } catch (error) {
    console.error('Failed to terminate session:', error)
  } finally {
    terminating.value = false
  }
}

const refreshSession = () => {
  emit('refresh')
}

const viewDetails = () => {
  showDetailsDialog.value = true
}

const formatDate = (date) => {
  return new Date(date).toLocaleString()
}
</script>

<style scoped>
.session-card {
  transition: all 0.3s ease;
}

.session-card:hover {
  transform: translateY(-2px);
}

.current-session {
  border: 2px solid rgb(var(--v-theme-primary));
}

.font-mono {
  font-family: 'Roboto Mono', monospace;
  font-size: 0.875rem;
}

.gap-2 {
  gap: 8px;
}
</style>
