<template>
  <div>
    <!-- Project Selection (Required for all tasks) -->
    <div class="mb-4">
      <v-select
        v-model="editableTask.project_id"
        :items="projectOptions"
        :loading="projectsLoading"
        :rules="validationRules.project_id"
        density="compact"
        hide-details="auto"
        label="Project"
        prepend-inner-icon="mdi-folder"
        required
        variant="outlined"
      >
        <template #item="{ props, item }">
          <v-list-item v-bind="props">
            <template #prepend>
              <v-avatar color="primary" size="32">
                <v-icon color="white" size="16">mdi-folder</v-icon>
              </v-avatar>
            </template>
            <v-list-item-title>{{ item.title }}</v-list-item-title>
            <v-list-item-subtitle>{{ item.subtitle }}</v-list-item-subtitle>
          </v-list-item>
        </template>
      </v-select>
    </div>

    <!-- Task ID for New Tasks -->
    <div v-if="isNew" class="mb-3">
      <v-text-field
        v-model="editableTask.task_id"
        density="compact"
        hide-details="auto"
        label="Task ID"
        placeholder="Enter custom task ID (e.g., PROJ-123)"
        prepend-inner-icon="mdi-identifier"
        :rules="validationRules.task_id"
        variant="outlined"
      />
      <div class="d-flex align-center mt-1">
        <v-btn
          color="primary"
          prepend-icon="mdi-auto-fix"
          size="small"
          variant="text"
          @click="generateLocalTaskId"
        >
          Generate ID
        </v-btn>
        <v-spacer />
        <div class="text-caption text-medium-emphasis">
          Format: letters, numbers, and hyphens only
        </div>
      </div>
    </div>

    <!-- Summary -->
    <v-text-field
      v-model="editableTask.summary"
      class="mb-2"
      density="compact"
      hide-details="auto"
      label="Summary"
      :rules="validationRules.summary"
      variant="outlined"
    />
    <div v-if="!isNew" class="text-body-2 text-medium-emphasis mb-4">
      Task ID: {{ originalTaskCopy.task_id }} (Read-only)
    </div>
    <div v-else class="text-body-2 text-medium-emphasis mb-4">
      Enter a unique task ID above or generate one.
    </div>

    <v-row>
      <v-col cols="12" md="6">
        <div class="d-flex flex-column ga-3">
          <v-select
            v-model="editableTask.type"
            density="compact"
            hide-details
            :items="typeOptions"
            label="Type"
            variant="outlined"
          />
          <v-select
            v-model="editableTask.priority"
            density="compact"
            hide-details
            :items="priorityOptions"
            label="Priority"
            variant="outlined"
          />
          <v-select
            v-model="editableTask.status"
            density="compact"
            hide-details
            :items="statusOptions"
            label="Status"
            variant="outlined"
          />
          <v-select
            v-model="editableTask.estimated_effort"
            density="compact"
            hide-details
            :items="effortOptions"
            label="Estimated Effort"
            variant="outlined"
          />
          <v-text-field
            v-model="editableTask.epic"
            density="compact"
            hide-details
            label="Epic"
            placeholder="Optional epic name"
            variant="outlined"
          />
        </div>
      </v-col>
      <v-col cols="12" md="6">
        <!-- Linked Tasks Management -->
        <div class="mt-0"> <!-- Adjusted margin top -->
          <h4 class="text-subtitle-2 mb-3">
            <v-icon class="me-2">mdi-link</v-icon>
            Linked Tasks
          </h4>

          <!-- Existing Linked Tasks -->
          <div v-if="editableTask.linked_tasks && editableTask.linked_tasks.length > 0" class="mb-3">
            <v-chip
              v-for="(linkedTask, index) in editableTask.linked_tasks"
              :key="`${linkedTask.task_id}-${index}`"
              class="me-2 mb-2"
              closable
              color="primary"
              variant="outlined"
              @click:close="removeLinkedTask(index)"
            >
              <v-icon start>mdi-link-variant</v-icon>
              {{ linkedTask.task_id }} ({{ linkedTask.linkType }})
            </v-chip>
          </div>
          <div v-else class="text-caption text-medium-emphasis mb-3">
            No tasks linked yet.
          </div>

          <!-- Add New Linked Task -->
          <v-card class="pa-3" color="grey-lighten-5" variant="outlined">
            <div class="text-subtitle-2 mb-2">Add Linked Task</div>
            <v-row dense>
              <v-col cols="12" md="6">
                <v-autocomplete
                  v-model="newLinkedTask.task_id"
                  v-model:search="autocompleteSearch"
                  clearable
                  density="compact"
                  hide-details
                  item-title="title"
                  item-value="value"
                  :items="autocompleteItems"
                  label="Search Task ID"
                  :loading="autocompleteLoading"
                  no-data-text="No matching tasks found"
                  placeholder="Type to search tasks..."
                  prepend-inner-icon="mdi-magnify"
                  variant="outlined"
                  @update:model-value="onAutocompleteSelect"
                  @update:search="onAutocompleteSearchDebounced"
                >
                  <template #item="{ props: itemProps, item }">
                    <v-list-item
                      v-bind="itemProps"
                      :prepend-icon="item.raw.props.prependIcon"
                      :subtitle="item.raw.subtitle"
                      :title="item.raw.title"
                    >
                      <template #append>
                        <v-icon
                          :color="item.raw.props.appendIconColor"
                          size="small"
                        >
                          {{ item.raw.props.appendIcon }}
                        </v-icon>
                      </template>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="newLinkedTask.linkType"
                  density="compact"
                  hide-details
                  :items="linkTypes"
                  label="Link Type"
                  variant="outlined"
                />
              </v-col>
              <v-col class="d-flex align-center" cols="12" md="2">
                <v-btn
                  color="primary"
                  :disabled="!newLinkedTask.task_id"
                  icon="mdi-plus"
                  size="small"
                  variant="elevated"
                  @click="addLocalLinkedTask"
                />
              </v-col>
            </v-row>
            <div class="text-caption text-medium-emphasis mt-2">
              <strong>Parent:</strong> Linked task is a parent of this task.<br>
              <strong>Requires:</strong> This task requires the linked task.
            </div>
          </v-card>
        </div>
      </v-col>
    </v-row>

    <!-- Description Editor -->
    <div class="mt-6">
      <h4 class="text-subtitle-1 font-weight-medium mb-2">
        <v-icon class="me-2">mdi-text-box-outline</v-icon>
        Description
      </h4>
      <MdEditor
        v-model="editableTask.description"
        :height="300"
        language="en-US"
        placeholder="Enter task description using Markdown syntax..."
        preview-theme="vuepress"
        theme="light"
      />
    </div>

    <!-- Snackbar for local messages -->
    <v-snackbar
      v-model="showLocalSnackbar"
      :color="localSnackbarColor"
      timeout="3000"
    >
      {{ localSnackbarMessage }}
      <template #actions>
        <v-btn
          color="white"
          variant="text"
          @click="showLocalSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup>
  import { MdEditor } from 'md-editor-v3'
  import { computed, onMounted, ref, watch } from 'vue'
  import { useTasksStore } from '@/stores/tasks'
  import { useProjectsStore } from '@/stores/projects'
  import { getPriorityColor as getPriorityColorUtil, getTypeIcon as getTaskTypeIconUtil } from '@/utils/taskDisplayUtils' // Renamed to avoid conflict
  import 'md-editor-v3/lib/style.css'

  const props = defineProps({
    modelValue: { // Represents the task being edited or a template for a new task
      type: Object,
      required: true,
    },
    isNew: {
      type: Boolean,
      default: false,
    },
    existingTasks: { // Pass all tasks from the store for validation and autocomplete
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['update:modelValue', 'generate-task-id', 'validate-task-id'])

  const tasksStore = useTasksStore()
  const projectsStore = useProjectsStore()

  const editableTask = ref(structuredClone(props.modelValue)) // Deep copy
  const originalTaskCopy = ref(structuredClone(props.modelValue)) // For non-new tasks

  // Ensure linked_tasks is always an array
  if (!editableTask.value.linked_tasks) {
    editableTask.value.linked_tasks = []
  }

  // Snackbar for form-specific messages
  const showLocalSnackbar = ref(false)
  const localSnackbarMessage = ref('')
  const localSnackbarColor = ref('info')

  // Project-related reactive data
  const projectsLoading = ref(false)

  watch(() => props.modelValue, (newValue, _oldValue) => {
    // Only update if the prop value is actually different from current editableTask
    // This prevents circular updates when the parent updates the prop based on our emit
    const currentEditableTaskString = JSON.stringify(editableTask.value)
    const newValueString = JSON.stringify(newValue)

    if (currentEditableTaskString === newValueString) {
      return // No change needed, prevent circular update
    }

    editableTask.value = JSON.parse(newValueString)
    // Ensure linked_tasks is always an array
    if (!editableTask.value.linked_tasks) {
      editableTask.value.linked_tasks = []
    }
    if (!props.isNew) {
      originalTaskCopy.value = JSON.parse(newValueString)
    }
  }, { deep: true })

  watch(editableTask, (newValue, _oldValue) => {
    // Only emit if the editableTask is actually different from the current prop
    // This prevents emitting when we just updated editableTask from prop changes
    const currentPropString = JSON.stringify(props.modelValue)
    const newValueString = JSON.stringify(newValue)

    if (currentPropString === newValueString) {
      return // No change needed, prevent circular update
    }

    emit('update:modelValue', JSON.parse(newValueString))
  }, { deep: true })

  const typeOptions = ['Story', 'Task', 'Epic', 'Bug']
  const priorityOptions = ['High', 'Medium', 'Low']
  const statusOptions = ['Backlog', 'In Progress', 'Done', 'Blocked']
  const effortOptions = ['Large', 'Medium', 'Small', 'N/A'] // Added N/A
  const linkTypes = ['Parent', 'Requires']

  // --- Computed Properties ---
  const projectOptions = computed(() => {
    return projectsStore.projectOptions
  })

  // --- Validation Rules ---
  const validationRules = {
    summary: [
      v => !!v || 'Summary is required',
      v => (v && v.length >= 3) || 'Summary must be at least 3 characters',
    ],
    task_id: [
      v => {
        if (props.isNew) {
          if (!v) return 'Task ID is required'
          // Emit event for parent to validate using tasksStore
          const validationResult = tasksStore.validateTaskId(v) // Access store directly
          if (validationResult) return validationResult
        }
        return true
      },
    ],
    project_id: [
      v => !!v || 'Project is required',
    ],
  }

  // --- Task ID Generation ---
  const generateLocalTaskId = () => {
    editableTask.value.task_id = tasksStore.generateTaskId() // Access store directly
  }

  // --- Linked Tasks Management ---
  const newLinkedTask = ref({ task_id: '', linkType: 'Requires' })
  const autocompleteSearch = ref('')
  const autocompleteLoading = ref(false)
  let debounceTimeout = null

  const availableTasksForLinking = computed(() => {
    return props.existingTasks.filter(t => {
      const currentEditingTaskId = props.isNew ? editableTask.value.task_id : originalTaskCopy.value.task_id
      if (t.task_id === currentEditingTaskId && currentEditingTaskId) return false // Exclude self if ID is set
      const linkedTasks = editableTask.value.linked_tasks || []
      return !linkedTasks.some(link => link.task_id === t.task_id)
    })
  })

  const filteredTaskSuggestions = computed(() => {
    if (!autocompleteSearch.value) {
      return availableTasksForLinking.value.slice(0, 10)
    }
    const searchTerm = autocompleteSearch.value.toLowerCase()
    return availableTasksForLinking.value
      .filter(t => t.task_id.toLowerCase().includes(searchTerm) || t.summary.toLowerCase().includes(searchTerm))
      .slice(0, 10)
  })

  const autocompleteItems = computed(() => {
    return filteredTaskSuggestions.value.map(t => ({
      title: `[${t.task_id}] ${t.summary}`,
      value: t.task_id,
      subtitle: `${t.type} • ${t.priority} Priority • ${t.status}`,
      props: { // Pass props for v-list-item
        prependIcon: getTaskTypeIconUtil(t.type), // Use the renamed util
        appendIcon: getPriorityIcon(t.priority), // Keep local helper or move if general
        appendIconColor: getPriorityColorUtil(t.priority), // Use the renamed util
      },
    }))
  })

  const getPriorityIcon = priority => { // Local helper for specific icon
    switch (priority) {
      case 'High': { return 'mdi-arrow-up-bold-circle'
      }
      case 'Medium': { return 'mdi-minus-circle'
      }
      case 'Low': { return 'mdi-arrow-down-bold-circle'
      }
      default: { return 'mdi-help-circle'
      }
    }
  }

  const onAutocompleteSearchDebounced = searchInput => {
    if (debounceTimeout) clearTimeout(debounceTimeout)
    autocompleteLoading.value = true
    debounceTimeout = setTimeout(() => {
      autocompleteSearch.value = searchInput || ''
      autocompleteLoading.value = false
    }, 300)
  }

  const onAutocompleteSelect = selectedTaskId => {
    if (selectedTaskId) {
      newLinkedTask.value.task_id = selectedTaskId
      // Optionally, update the search text to reflect the selection,
      // or clear it if you prefer the user to explicitly add.
      const selectedItem = autocompleteItems.value.find(item => item.value === selectedTaskId)
      if (selectedItem) {
        autocompleteSearch.value = selectedItem.title
      }
    }
  }

  const addLocalLinkedTask = () => {
    const taskId = newLinkedTask.value.task_id?.trim()
    if (!taskId) {
      localSnackbarMessage.value = 'Please select a task to link.'
      localSnackbarColor.value = 'warning'
      showLocalSnackbar.value = true
      return
    }

    const validationError = tasksStore.validateLinkedTask(taskId) // Use store directly
    if (validationError && validationError !== `Linked Task ID "${taskId}" does not exist.`) { // Allow linking non-existent if desired, or handle differently
      // For now, we assume linked tasks must exist.
      localSnackbarMessage.value = validationError
      localSnackbarColor.value = 'error'
      showLocalSnackbar.value = true
      return
    }
    if (!props.existingTasks.some(t => t.task_id === taskId)) {
      localSnackbarMessage.value = `Task with ID "${taskId}" does not exist in the database.`
      localSnackbarColor.value = 'error'
      showLocalSnackbar.value = true
      return
    }

    const currentEditingTaskId = props.isNew ? editableTask.value.task_id : originalTaskCopy.value.task_id
    if (taskId === currentEditingTaskId && currentEditingTaskId) {
      localSnackbarMessage.value = 'Cannot link task to itself.'
      localSnackbarColor.value = 'error'
      showLocalSnackbar.value = true
      return
    }
    if (editableTask.value.linked_tasks.some(link => link.task_id === taskId)) {
      localSnackbarMessage.value = 'Task is already linked.'
      localSnackbarColor.value = 'warning'
      showLocalSnackbar.value = true
      return
    }

    editableTask.value.linked_tasks.push({ ...newLinkedTask.value })
    newLinkedTask.value = { task_id: '', linkType: 'Requires' }
    autocompleteSearch.value = '' // Clear search
    localSnackbarMessage.value = 'Linked task added to form.'
    localSnackbarColor.value = 'success'
    showLocalSnackbar.value = true
  }

  const removeLinkedTask = index => {
    if (editableTask.value.linked_tasks) {
      editableTask.value.linked_tasks.splice(index, 1)
      localSnackbarMessage.value = 'Linked task removed from form.'
      localSnackbarColor.value = 'info'
      showLocalSnackbar.value = true
    }
  }

  onMounted(async () => {
    // Fetch projects if not already loaded
    if (projectsStore.projects.length === 0) {
      projectsLoading.value = true
      try {
        await projectsStore.fetchProjects()
      } catch (error) {
        console.error('Failed to fetch projects:', error)
      } finally {
        projectsLoading.value = false
      }
    }

    // Set default project if creating new task and no project is selected
    if (props.isNew && !editableTask.value.project_id && projectsStore.selectedProject) {
      editableTask.value.project_id = projectsStore.selectedProject.id
    }

    if (props.isNew && !editableTask.value.task_id) {
    // Optional: generate an ID immediately for new tasks if not provided
    // generateLocalTaskId();
    }
  })

// Expose any methods parent might need to call (if any)
// defineExpose({});

</script>

<style scoped>
/* Add any specific styles for TaskForm.vue here */
.v-chip {
  font-weight: 500;
}
</style>
