<template>
  <v-list-item
    class="py-3 cursor-pointer"
    :style="{ paddingLeft: `${6 + (task.level || 0) * 24}px` }"
    @click="emit('viewTaskDetails', task.task_id)"
  >
    <template #prepend>
      <v-avatar :color="getPriorityColor(task.priority)" size="40">
        <v-icon color="white">{{ getTypeIcon(task.type) }}</v-icon>
      </v-avatar>
    </template>

    <v-list-item-title class="font-weight-medium">
      {{ task.summary }}
    </v-list-item-title>

    <v-list-item-subtitle class="mt-1">
      <div class="text-body-2 mb-1">{{ task.task_id }}</div>
      <div class="d-flex align-center flex-wrap ga-2 mb-1">
        <v-chip :color="getPriorityColor(task.priority)" size="small">
          {{ task.priority }}
        </v-chip>
        <v-chip size="small" variant="outlined">
          {{ task.type }}
        </v-chip>
        <v-chip
          :color="getStatusColor(task.status)"
          size="small"
          variant="tonal"
        >
          {{ task.status }}
        </v-chip>
        <span v-if="task.epic" class="text-caption">
          Epic: {{ task.epic }}
        </span>
      </div>
      <!-- Project Information -->
      <div v-if="projectName" class="d-flex align-center ga-1">
        <v-icon color="primary" size="14">mdi-folder</v-icon>
        <span class="text-caption text-primary">{{ projectName }}</span>
      </div>
    </v-list-item-subtitle>

    <template #append>
      <div class="d-flex align-center ga-2">
        <v-tooltip text="View Details">
          <template #activator="{ props: tooltipProps }">
            <v-btn
              v-bind="tooltipProps"
              icon="mdi-eye"
              size="small"
              variant="text"
              @click.stop="emit('viewTaskDetails', task.task_id)"
            />
          </template>
        </v-tooltip>
        <v-menu>
          <template #activator="{ props: menuProps }">
            <v-btn
              v-bind="menuProps"
              icon="mdi-dots-vertical"
              size="small"
              variant="text"
              @click.stop
            />
          </template>
          <v-list>
            <v-list-item @click="emit('updateStatus', task, 'Backlog')">
              <v-list-item-title>Move to Backlog</v-list-item-title>
            </v-list-item>
            <v-list-item @click="emit('updateStatus', task, 'In Progress')">
              <v-list-item-title>Start Progress</v-list-item-title>
            </v-list-item>
            <v-list-item @click="emit('updateStatus', task, 'Done')">
              <v-list-item-title>Mark Done</v-list-item-title>
            </v-list-item>
            <v-list-item @click="emit('updateStatus', task, 'Blocked')">
              <v-list-item-title>Mark Blocked</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </template>
  </v-list-item>
</template>

<script setup>
import { computed } from 'vue'
import {
  getPriorityColor,
  getStatusColor,
  getTypeIcon,
} from '@/utils/taskDisplayUtils';

const props = defineProps({
  task: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['viewTaskDetails', 'updateStatus']);

// Computed properties
const projectName = computed(() => {
  // Check if task has expanded project data
  if (props.task.expand?.project_id) {
    return props.task.expand.project_id.name
  }
  // Fallback to project_id if no expanded data
  return null
})
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
.v-chip {
  font-weight: 500;
}
</style>
