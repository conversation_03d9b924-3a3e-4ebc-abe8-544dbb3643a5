<template>
  <v-card class="mb-4" elevation="2">
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon class="mr-2" color="primary">mdi-folder-outline</v-icon>
        <span>Project</span>
      </div>
      <v-btn
        color="primary"
        icon="mdi-plus"
        size="small"
        variant="text"
        @click="showCreateDialog = true"
      />
    </v-card-title>

    <v-card-text>
      <v-select
        v-model="selectedProjectId"
        :items="projectsStore.projectOptions"
        :loading="projectsStore.loading"
        :error-messages="projectsStore.error"
        density="compact"
        hide-details="auto"
        label="Select Project"
        prepend-inner-icon="mdi-folder"
        variant="outlined"
        @update:model-value="onProjectChange"
      >
        <template #item="{ props, item }">
          <v-list-item v-bind="props">
            <template #prepend>
              <v-avatar color="primary" size="32">
                <v-icon color="white" size="16">mdi-folder</v-icon>
              </v-avatar>
            </template>
            <v-list-item-title>{{ item.title }}</v-list-item-title>
            <v-list-item-subtitle>{{ item.subtitle }}</v-list-item-subtitle>
          </v-list-item>
        </template>
      </v-select>

      <!-- Project Statistics -->
      <div v-if="selectedProject" class="mt-3">
        <v-row>
          <v-col cols="3">
            <v-card color="primary" variant="tonal">
              <v-card-text class="text-center">
                <div class="text-h6">{{ projectStats.totalTasks }}</div>
                <div class="text-caption">Total Tasks</div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="3">
            <v-card color="success" variant="tonal">
              <v-card-text class="text-center">
                <div class="text-h6">{{ projectStats.completedTasks }}</div>
                <div class="text-caption">Completed</div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="3">
            <v-card color="warning" variant="tonal">
              <v-card-text class="text-center">
                <div class="text-h6">{{ projectStats.inProgressTasks }}</div>
                <div class="text-caption">In Progress</div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="3">
            <v-card color="info" variant="tonal">
              <v-card-text class="text-center">
                <div class="text-h6">{{ projectStats.completionRate }}%</div>
                <div class="text-caption">Complete</div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-card-text>

    <!-- Create Project Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="500">
      <v-card>
        <v-card-title>Create New Project</v-card-title>
        <v-card-text>
          <v-form ref="createForm" @submit.prevent="createProject">
            <v-text-field
              v-model="newProject.name"
              :rules="nameRules"
              density="compact"
              hide-details="auto"
              label="Project Name"
              prepend-inner-icon="mdi-folder"
              required
              variant="outlined"
            />
            <v-textarea
              v-model="newProject.description"
              class="mt-3"
              density="compact"
              hide-details="auto"
              label="Description (Optional)"
              prepend-inner-icon="mdi-text"
              rows="3"
              variant="outlined"
            />
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showCreateDialog = false">Cancel</v-btn>
          <v-btn
            :loading="creating"
            color="primary"
            variant="elevated"
            @click="createProject"
          >
            Create Project
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useProjectsStore } from '@/stores/projects'
import { useTasksStore } from '@/stores/tasks'

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'project-changed'])

// Stores
const projectsStore = useProjectsStore()
const tasksStore = useTasksStore()

// Local state
const selectedProjectId = ref(props.modelValue)
const showCreateDialog = ref(false)
const creating = ref(false)
const createForm = ref(null)

const newProject = ref({
  name: '',
  description: '',
})

// Computed
const selectedProject = computed(() => {
  return selectedProjectId.value
    ? projectsStore.getProjectById(selectedProjectId.value)
    : null
})

const projectStats = computed(() => {
  if (!selectedProjectId.value) {
    return { totalTasks: 0, completedTasks: 0, inProgressTasks: 0, completionRate: 0 }
  }
  return tasksStore.getProjectStatistics(selectedProjectId.value)
})

// Validation rules
const nameRules = [
  v => !!v || 'Project name is required',
  v => (v && v.length >= 2) || 'Project name must be at least 2 characters',
  v => (v && v.length <= 100) || 'Project name must be less than 100 characters',
]

// Methods
const onProjectChange = (projectId) => {
  // Update local state first
  selectedProjectId.value = projectId
  
  // Get the project object using the new projectId
  const project = projectId ? projectsStore.getProjectById(projectId) : null
  
  // Emit the updates
  emit('update:modelValue', projectId)
  emit('project-changed', project)
  projectsStore.selectProject(project)
}

const createProject = async () => {
  if (!createForm.value) return

  const { valid } = await createForm.value.validate()
  if (!valid) return

  creating.value = true
  try {
    const createdProject = await projectsStore.addProject(newProject.value)

    if (createdProject) {
      // Select the newly created project
      selectedProjectId.value = createdProject.id
      onProjectChange(createdProject.id)

      // Reset form and close dialog
      newProject.value = { name: '', description: '' }
      showCreateDialog.value = false
      createForm.value?.reset()
    }
  } catch (error) {
    console.error('Failed to create project:', error)
  } finally {
    creating.value = false
  }
}

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  selectedProjectId.value = newValue
})

// Lifecycle
onMounted(async () => {
  // Fetch projects if not already loaded or loading
  if (projectsStore.projects.length === 0 && !projectsStore.loading) {
    try {
      await projectsStore.fetchProjects()
    } catch (error) {
      console.error('Failed to fetch projects in ProjectSelector:', error)
    }
  }

  // Set initial selection if provided
  if (props.modelValue) {
    selectedProjectId.value = props.modelValue
  } else if (projectsStore.selectedProject) {
    selectedProjectId.value = projectsStore.selectedProject.id
    emit('update:modelValue', selectedProjectId.value)
  }
})
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}
</style>
