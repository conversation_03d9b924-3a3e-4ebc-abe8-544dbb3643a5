import { defineStore } from 'pinia'
import { pocketbaseService } from '../../common/services/pocketbase.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
    error: null,
    initialized: false,
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && !!state.token,
    currentUser: (state) => state.user,
    authToken: (state) => state.token,
    isLoading: (state) => state.loading,
    authError: (state) => state.error,
    isInitialized: (state) => state.initialized,
  },

  actions: {
    async login(credentials) {
      this.loading = true
      this.error = null
      
      try {
        const response = await pocketbaseService.login(credentials.email, credentials.password)
        
        if (response.success) {
          this.user = response.user
          this.token = response.token
          this.isAuthenticated = true
          
          // Persist to localStorage
          this.persistAuthState()
          
          return { success: true, user: response.user }
        } else {
          this.error = response.error || '<PERSON><PERSON> failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || '<PERSON><PERSON> failed'
        this.clearAuthState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async logout() {
      this.loading = true
      this.error = null
      
      try {
        const response = await pocketbaseService.logout()
        
        this.clearAuthState()
        this.clearPersistedState()
        
        return { success: true }
      } catch (error) {
        this.error = error.message || 'Logout failed'
        // Clear local state even if API call fails
        this.clearAuthState()
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async refreshToken() {
      if (!this.token) return { success: false, error: 'No token available' }
      
      this.loading = true
      this.error = null
      
      try {
        // PocketBase handles token refresh automatically
        const isAuthenticated = await pocketbaseService.isAuthenticated()
        
        if (isAuthenticated.success) {
          this.token = isAuthenticated.token
          this.persistAuthState()
          return { success: true, token: isAuthenticated.token }
        } else {
          this.clearAuthState()
          this.clearPersistedState()
          return { success: false, error: 'Token refresh failed' }
        }
      } catch (error) {
        this.error = error.message || 'Token refresh failed'
        this.clearAuthState()
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async initializeAuth() {
      this.loading = true
      this.error = null
      
      try {
        const isAuthenticated = await pocketbaseService.isAuthenticated()
        
        if (isAuthenticated.success) {
          this.user = isAuthenticated.user
          this.token = isAuthenticated.token
          this.isAuthenticated = true
          this.persistAuthState()
          return { success: true, user: this.user }
        } else {
          this.clearAuthState()
          this.clearPersistedState()
          return { success: false, error: 'No valid session found' }
        }
      } catch (error) {
        this.error = error.message || 'Auth initialization failed'
        this.clearAuthState()
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
        this.initialized = true
      }
    },

    async register(userData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await pocketbaseService.register(userData)
        
        if (response.success) {
          this.user = response.user
          this.token = response.token
          this.isAuthenticated = true
          
          // Persist to localStorage
          this.persistAuthState()
          
          return { success: true, user: response.user }
        } else {
          this.error = response.error || 'Registration failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        this.error = error.message || 'Registration failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async updateProfile(userData) {
      this.loading = true
      this.error = null
      
      try {
        // For now, just update local state
        // TODO: Implement actual profile update with PocketBase
        this.user = { ...this.user, ...userData }
        this.persistAuthState()
        
        return { success: true, user: this.user }
      } catch (error) {
        this.error = error.message || 'Profile update failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async requestPasswordReset(email) {
      this.loading = true
      this.error = null
      
      try {
        const response = await pocketbaseService.requestPasswordReset(email)
        return response
      } catch (error) {
        this.error = error.message || 'Password reset request failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async confirmPasswordReset(token, password) {
      this.loading = true
      this.error = null
      
      try {
        const response = await pocketbaseService.confirmPasswordReset(token, password)
        return response
      } catch (error) {
        this.error = error.message || 'Password reset confirmation failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    clearError() {
      this.error = null
    },

    clearAuthState() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.error = null
    },

    persistAuthState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('auth_user', JSON.stringify(this.user))
        localStorage.setItem('auth_token', this.token)
      }
    },

    getPersistedState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        const user = localStorage.getItem('auth_user')
        const token = localStorage.getItem('auth_token')
        
        return {
          user: user ? JSON.parse(user) : null,
          token: token || null,
        }
      }
      
      return { user: null, token: null }
    },

    clearPersistedState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem('auth_user')
        localStorage.removeItem('auth_token')
      }
    },
  },
})
