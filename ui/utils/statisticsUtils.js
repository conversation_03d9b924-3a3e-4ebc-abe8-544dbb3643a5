/**
 * Statistics utilities
 * Provides reusable functions for computing statistics and aggregations
 */

/**
 * Groups items by a specific property
 * @param {Array} items - Array of items to group
 * @param {string|Function} groupBy - Property name or function to group by
 * @returns {Object} Object with groups as keys and arrays of items as values
 */
export const groupBy = (items, groupBy) => {
  if (!Array.isArray(items)) return {}
  
  const getGroupKey = typeof groupBy === 'function' 
    ? groupBy 
    : item => item[groupBy]

  return items.reduce((groups, item) => {
    const key = getGroupKey(item)
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
    return groups
  }, {})
}

/**
 * Counts items by a specific property
 * @param {Array} items - Array of items to count
 * @param {string|Function} countBy - Property name or function to count by
 * @returns {Object} Object with keys and their counts
 */
export const countBy = (items, countBy) => {
  const groups = groupBy(items, countBy)
  return Object.keys(groups).reduce((counts, key) => {
    counts[key] = groups[key].length
    return counts
  }, {})
}

/**
 * Converts count object to array format suitable for charts/displays
 * @param {Object} counts - Object with keys and count values
 * @param {string} keyName - Name for the key property (default: 'name')
 * @param {string} valueName - Name for the value property (default: 'count')
 * @returns {Array} Array of objects with key and count properties
 */
export const countsToArray = (counts, keyName = 'name', valueName = 'count') => {
  return Object.entries(counts).map(([key, count]) => ({
    [keyName]: key,
    [valueName]: count,
  }))
}

/**
 * Calculates percentage distribution
 * @param {Object} counts - Object with keys and count values
 * @param {number} precision - Number of decimal places (default: 1)
 * @returns {Object} Object with keys and percentage values
 */
export const calculatePercentages = (counts, precision = 1) => {
  const total = Object.values(counts).reduce((sum, count) => sum + count, 0)
  
  if (total === 0) return {}
  
  return Object.keys(counts).reduce((percentages, key) => {
    percentages[key] = parseFloat(((counts[key] / total) * 100).toFixed(precision))
    return percentages
  }, {})
}

/**
 * Gets the most common value(s) from an array
 * @param {Array} items - Array of items
 * @param {string|Function} property - Property to analyze or function to extract value
 * @returns {Array} Array of most common values
 */
export const getMostCommon = (items, property = null) => {
  const getValue = property 
    ? (typeof property === 'function' ? property : item => item[property])
    : item => item

  const counts = countBy(items, getValue)
  const maxCount = Math.max(...Object.values(counts))
  
  return Object.keys(counts).filter(key => counts[key] === maxCount)
}

/**
 * Calculates completion rate for tasks
 * @param {Array} tasks - Array of task objects
 * @param {string} statusField - Field name for status (default: 'status')
 * @param {string|Array} completedStatuses - Status(es) considered completed
 * @returns {number} Completion rate as percentage (0-100)
 */
export const calculateCompletionRate = (
  tasks, 
  statusField = 'status', 
  completedStatuses = 'Done'
) => {
  if (!Array.isArray(tasks) || tasks.length === 0) return 0
  
  const completedStatusSet = Array.isArray(completedStatuses) 
    ? new Set(completedStatuses) 
    : new Set([completedStatuses])
    
  const completedCount = tasks.filter(task => 
    completedStatusSet.has(task[statusField])
  ).length
  
  return Math.round((completedCount / tasks.length) * 100)
}

/**
 * Creates comprehensive statistics for a collection of tasks
 * @param {Array} tasks - Array of task objects
 * @param {Object} options - Configuration options
 * @returns {Object} Statistics object
 */
export const generateTaskStatistics = (tasks, options = {}) => {
  const {
    typeField = 'type',
    priorityField = 'priority',
    statusField = 'status',
    epicField = 'epic',
    completedStatuses = ['Done'],
  } = options

  if (!Array.isArray(tasks)) {
    return createEmptyStatistics()
  }

  const total = tasks.length

  // Basic counts
  const byType = countBy(tasks, typeField)
  const byPriority = countBy(tasks, priorityField)
  const byStatus = countBy(tasks, statusField)
  
  // Epic stats (filter out null/undefined epics)
  const epics = tasks
    .filter(task => task[epicField])
    .map(task => task[epicField])
  const uniqueEpics = [...new Set(epics)].sort()

  // Completion stats
  const completedStatusSet = new Set(completedStatuses)
  const completed = tasks.filter(task => completedStatusSet.has(task[statusField])).length
  const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

  return {
    total,
    completed,
    completionRate,
    byType: {
      counts: byType,
      array: countsToArray(byType, 'type', 'count'),
      percentages: calculatePercentages(byType),
    },
    byPriority: {
      counts: byPriority,
      array: countsToArray(byPriority, 'priority', 'count'),
      percentages: calculatePercentages(byPriority),
    },
    byStatus: {
      counts: byStatus,
      array: countsToArray(byStatus, 'status', 'count'),
      percentages: calculatePercentages(byStatus),
    },
    epics: uniqueEpics,
    epicCount: uniqueEpics.length,
  }
}

/**
 * Creates comprehensive statistics for project tasks
 * @param {Array} tasks - Array of task objects for a specific project
 * @returns {Object} Project-specific statistics
 */
export const generateProjectStatistics = (tasks) => {
  const baseStats = generateTaskStatistics(tasks)
  
  // Additional project-specific metrics
  const inProgress = tasks.filter(task => task.status === 'In Progress').length
  const backlog = tasks.filter(task => task.status === 'Backlog').length
  const blocked = tasks.filter(task => task.status === 'Blocked').length

  return {
    ...baseStats,
    totalTasks: baseStats.total,
    completedTasks: baseStats.completed,
    inProgressTasks: inProgress,
    backlogTasks: backlog,
    blockedTasks: blocked,
  }
}

/**
 * Creates an empty statistics object
 * @returns {Object} Empty statistics object with zero values
 */
export const createEmptyStatistics = () => ({
  total: 0,
  completed: 0,
  completionRate: 0,
  byType: { counts: {}, array: [], percentages: {} },
  byPriority: { counts: {}, array: [], percentages: {} },
  byStatus: { counts: {}, array: [], percentages: {} },
  epics: [],
  epicCount: 0,
})

/**
 * Filters tasks and generates statistics
 * @param {Array} tasks - Array of task objects
 * @param {Object} filters - Filter criteria
 * @returns {Object} Statistics for filtered tasks
 */
export const getFilteredStatistics = (tasks, filters = {}) => {
  let filteredTasks = [...tasks]

  // Apply filters
  Object.entries(filters).forEach(([field, value]) => {
    if (value && value !== 'All') {
      filteredTasks = filteredTasks.filter(task => task[field] === value)
    }
  })

  return generateTaskStatistics(filteredTasks)
}

/**
 * Compares statistics between two periods or sets
 * @param {Object} current - Current period statistics
 * @param {Object} previous - Previous period statistics
 * @returns {Object} Comparison object with changes and trends
 */
export const compareStatistics = (current, previous) => {
  const getTrend = (currentValue, previousValue) => {
    if (previousValue === 0) return currentValue > 0 ? 'up' : 'stable'
    const change = ((currentValue - previousValue) / previousValue) * 100
    if (Math.abs(change) < 5) return 'stable'
    return change > 0 ? 'up' : 'down'
  }

  const getChange = (currentValue, previousValue) => {
    return currentValue - previousValue
  }

  const getPercentChange = (currentValue, previousValue) => {
    if (previousValue === 0) return currentValue > 0 ? 100 : 0
    return Math.round(((currentValue - previousValue) / previousValue) * 100)
  }

  return {
    total: {
      current: current.total,
      previous: previous.total,
      change: getChange(current.total, previous.total),
      percentChange: getPercentChange(current.total, previous.total),
      trend: getTrend(current.total, previous.total),
    },
    completed: {
      current: current.completed,
      previous: previous.completed,
      change: getChange(current.completed, previous.completed),
      percentChange: getPercentChange(current.completed, previous.completed),
      trend: getTrend(current.completed, previous.completed),
    },
    completionRate: {
      current: current.completionRate,
      previous: previous.completionRate,
      change: getChange(current.completionRate, previous.completionRate),
      trend: getTrend(current.completionRate, previous.completionRate),
    },
  }
}

/**
 * Utility functions for working with time-based statistics
 */
export const timeBasedStats = {
  /**
   * Groups tasks by creation date
   * @param {Array} tasks - Array of task objects
   * @param {string} dateField - Field containing the date (default: 'created')
   * @param {string} grouping - Grouping type ('day', 'week', 'month', 'year')
   * @returns {Object} Tasks grouped by time period
   */
  groupByPeriod: (tasks, dateField = 'created', grouping = 'day') => {
    const formatDate = (date, type) => {
      const d = new Date(date)
      switch (type) {
        case 'day':
          return d.toISOString().split('T')[0]
        case 'week':
          const week = Math.ceil((d.getDate() - d.getDay() + 1) / 7)
          return `${d.getFullYear()}-W${week}`
        case 'month':
          return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`
        case 'year':
          return d.getFullYear().toString()
        default:
          return d.toISOString().split('T')[0]
      }
    }

    return groupBy(tasks, task => formatDate(task[dateField], grouping))
  },

  /**
   * Calculates velocity (tasks completed per time period)
   * @param {Array} tasks - Array of completed tasks
   * @param {string} completedDateField - Field containing completion date
   * @param {string} grouping - Time period grouping
   * @returns {Array} Velocity data points
   */
  calculateVelocity: (tasks, completedDateField = 'updated', grouping = 'week') => {
    const grouped = timeBasedStats.groupByPeriod(tasks, completedDateField, grouping)
    return countsToArray(grouped, 'period', 'velocity')
  },
}
