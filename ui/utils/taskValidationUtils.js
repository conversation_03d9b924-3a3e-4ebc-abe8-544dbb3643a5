// src/utils/taskValidationUtils.js

/**
 * Validates a task ID.
 * Task ID format: alphanumeric with hyphens, 1-50 characters.
 * Cannot start or end with a hyphen if it contains hyphens.
 * @param {string} taskId - The task ID to validate.
 * @returns {string|null} - Error message string if invalid, null if valid.
 */
export const validateTaskIdFormat = taskId => {
  if (!taskId || taskId.length === 0) {
    return 'Task ID cannot be empty.';
  }
  if (taskId.length > 50) {
    return 'Task ID must be 50 characters or less.';
  }

  // Regex: Allows alphanumeric characters and hyphens.
  // Hyphens cannot be at the beginning or end.
  // Allows single character IDs (e.g., 'A') or multi-character IDs like 'TASK-1'.
  const taskIdRegex = /^[A-Za-z0-9]$|^[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9]$/;
  if (!taskIdRegex.test(taskId)) {
    return 'Task ID can only contain letters, numbers, and hyphens (cannot start or end with a hyphen unless it is a single character).';
  }

  return null; // Valid
};

/**
 * Checks if a task ID already exists in a list of tasks.
 * @param {string} taskId - The task ID to check.
 * @param {Array} existingTasks - Array of task objects to check against. Each task object should have a `task_id` property.
 * @returns {boolean} - True if the ID exists, false otherwise.
 */
export const checkTaskIdExists = (taskId, existingTasks = []) => {
  if (!taskId) return false;
  return existingTasks.some(task => task.task_id === taskId);
};

/**
 * Validates a task ID, including format and uniqueness.
 * @param {string} taskId - The task ID to validate.
 * @param {Array} existingTasks - Array of task objects for uniqueness check.
 * @returns {string|null} - Error message string if invalid, null if valid.
 */
export const validateTaskId = (taskId, existingTasks = []) => {
  const formatError = validateTaskIdFormat(taskId);
  if (formatError) {
    return formatError;
  }
  if (checkTaskIdExists(taskId, existingTasks)) {
    return `Task ID "${taskId}" already exists.`;
  }
  return null;
};

/**
 * Validates a linked task ID.
 * Checks format and existence in a list of tasks.
 * @param {string} taskId - The linked task ID to validate.
 * @param {Array} existingTasks - Array of task objects for existence check.
 * @returns {string|null} - Error message string if invalid, null if valid.
 */
export const validateLinkedTask = (taskId, existingTasks = []) => {
  if (!taskId || !taskId.trim()) {
    return 'Linked Task ID cannot be empty.';
  }
  const trimmedTaskId = taskId.trim();
  const formatError = validateTaskIdFormat(trimmedTaskId);
  if (formatError) {
    return formatError;
  }
  if (!checkTaskIdExists(trimmedTaskId, existingTasks)) {
    return `Linked Task ID "${trimmedTaskId}" does not exist.`;
  }
  return null;
};

/**
 * Generates a unique task ID.
 * Format: TASK-TIMESTAMP-RANDOMSUFFIX
 * Ensures the generated ID does not already exist in the provided list of tasks.
 * @param {Array} existingTasks - Array of task objects to check for uniqueness.
 * @returns {string} - A unique task ID.
 */
export const generateTaskId = (existingTasks = []) => {
  let newId;
  do {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).slice(2, 7).toUpperCase(); // Increased suffix length
    newId = `TASK-${timestamp}-${randomSuffix}`;
  } while (checkTaskIdExists(newId, existingTasks));
  return newId;
};
