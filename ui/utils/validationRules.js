/**
 * Reusable validation rules for Vue forms
 * These can be used across different components to ensure consistency
 */

/**
 * Basic validation rule creators
 */
export const validationRules = {
  /**
   * Creates a required field validation rule
   * @param {string} fieldName - Name of the field for error message
   * @returns {Function} Validation rule function
   */
  required: (fieldName = 'Field') => {
    return v => !!v || `${fieldName} is required`
  },

  /**
   * Creates a minimum length validation rule
   * @param {number} minLength - Minimum required length
   * @param {string} fieldName - Name of the field for error message
   * @returns {Function} Validation rule function
   */
  minLength: (minLength, fieldName = 'Field') => {
    return v => (v && v.length >= minLength) || `${fieldName} must be at least ${minLength} characters`
  },

  /**
   * Creates a maximum length validation rule
   * @param {number} maxLength - Maximum allowed length
   * @param {string} fieldName - Name of the field for error message
   * @returns {Function} Validation rule function
   */
  maxLength: (maxLength, fieldName = 'Field') => {
    return v => (!v || v.length <= maxLength) || `${fieldName} must be ${maxLength} characters or less`
  },

  /**
   * Creates an email validation rule
   * @returns {Function} Validation rule function
   */
  email: () => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return v => !v || emailPattern.test(v) || 'Email must be valid'
  },

  /**
   * Creates a custom pattern validation rule
   * @param {RegExp} pattern - Regular expression pattern
   * @param {string} message - Error message
   * @returns {Function} Validation rule function
   */
  pattern: (pattern, message) => {
    return v => !v || pattern.test(v) || message
  },

  /**
   * Creates a numeric validation rule
   * @param {string} fieldName - Name of the field for error message
   * @returns {Function} Validation rule function
   */
  numeric: (fieldName = 'Field') => {
    return v => !v || !isNaN(v) || `${fieldName} must be a number`
  },

  /**
   * Creates a range validation rule for numbers
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {string} fieldName - Name of the field for error message
   * @returns {Function} Validation rule function
   */
  range: (min, max, fieldName = 'Field') => {
    return v => {
      if (!v) return true
      const num = parseFloat(v)
      return (num >= min && num <= max) || `${fieldName} must be between ${min} and ${max}`
    }
  },
}

/**
 * Pre-configured validation rule sets for common entities
 */
export const entityValidationRules = {
  /**
   * Validation rules for task summary
   */
  taskSummary: [
    validationRules.required('Summary'),
    validationRules.minLength(3, 'Summary'),
    validationRules.maxLength(200, 'Summary'),
  ],

  /**
   * Validation rules for task ID
   * @param {Function} customTaskIdValidator - Custom validator function (e.g., from store)
   * @returns {Array} Validation rules array
   */
  taskId: (customTaskIdValidator) => [
    validationRules.required('Task ID'),
    validationRules.maxLength(50, 'Task ID'),
    validationRules.pattern(
      /^[A-Za-z0-9]$|^[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9]$/,
      'Task ID can only contain letters, numbers, and hyphens (cannot start or end with a hyphen unless it is a single character)'
    ),
    ...(customTaskIdValidator ? [customTaskIdValidator] : []),
  ],

  /**
   * Validation rules for project name
   */
  projectName: [
    validationRules.required('Project name'),
    validationRules.minLength(2, 'Project name'),
    validationRules.maxLength(100, 'Project name'),
  ],

  /**
   * Validation rules for project description
   */
  projectDescription: [
    validationRules.maxLength(500, 'Project description'),
  ],

  /**
   * Validation rules for general text fields
   */
  text: (fieldName, required = false, minLength = 0, maxLength = 255) => {
    const rules = []
    if (required) rules.push(validationRules.required(fieldName))
    if (minLength > 0) rules.push(validationRules.minLength(minLength, fieldName))
    if (maxLength > 0) rules.push(validationRules.maxLength(maxLength, fieldName))
    return rules
  },
}

/**
 * Utility functions for validation
 */
export const validationUtils = {
  /**
   * Combines multiple validation rule arrays
   * @param {...Array} ruleArrays - Arrays of validation rules
   * @returns {Array} Combined array of validation rules
   */
  combine: (...ruleArrays) => {
    return ruleArrays.flat()
  },

  /**
   * Creates a conditional validation rule
   * @param {Function} condition - Function that returns true when validation should apply
   * @param {Function} rule - Validation rule to apply when condition is true
   * @returns {Function} Conditional validation rule
   */
  conditional: (condition, rule) => {
    return v => condition() ? rule(v) : true
  },

  /**
   * Creates an async validation rule (useful for server-side validation)
   * @param {Function} asyncValidator - Async function that returns true if valid, error message if invalid
   * @returns {Function} Async validation rule
   */
  async: (asyncValidator) => {
    return async v => {
      try {
        const result = await asyncValidator(v)
        return result === true ? true : result
      } catch (error) {
        return `Validation error: ${error.message}`
      }
    }
  },
}

/**
 * Common validation patterns
 */
export const patterns = {
  // Task ID pattern (alphanumeric with hyphens, not starting/ending with hyphen)
  taskId: /^[A-Za-z0-9]$|^[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9]$/,
  
  // Email pattern
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // URL pattern
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // Phone number pattern (basic)
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  
  // Alphanumeric only
  alphanumeric: /^[a-zA-Z0-9]+$/,
  
  // No special characters except spaces, hyphens, and underscores
  safeText: /^[a-zA-Z0-9\s\-_]+$/,
}
