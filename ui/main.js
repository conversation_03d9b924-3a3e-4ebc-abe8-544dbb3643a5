/**
 * main.js
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Plugins
import { registerPlugins } from '@/plugins'

// Components
import App from './App.vue'

// Composables
import { createApp } from 'vue'

// Styles
import 'unfonts.css'

// Database
import databaseService from '@common/services/databaseService'
import databaseInitializer from '@/services/databaseInitializer'

async function startApp() {
  try {
    // Authenticate with PocketBase before doing anything else
    await databaseService.authenticate();

    // Initialize database schema and ensure default project exists
    await databaseInitializer.initialize();

    const app = createApp(App)
    registerPlugins(app)
    app.mount('#app')

  } catch (error) {
    console.error("Failed to start the application:", error)
    // Display a user-friendly error message if authentication or app setup fails
    document.getElementById('app').innerHTML = `
      <div style="font-family: sans-serif; text-align: center; padding: 40px;">
        <h1>Initialization Error</h1>
        <p>Could not connect to the backend service.</p>
        <p>Please ensure the service is running and accessible.</p>
        <p style="color: #666; font-size: 0.9em;">Check the browser console for more details.</p>
      </div>
    `
  }
}

startApp();
