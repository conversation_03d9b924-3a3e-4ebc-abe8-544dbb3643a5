/**
 * router/index.js
 *
 * Automatic routes for `./ui/pages/*.vue`
 */

// Composables
import { createRouter, createWebHistory } from 'vue-router/auto'
import { setupLayouts } from 'virtual:generated-layouts'
import { routes } from 'vue-router/auto-routes'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: setupLayouts(routes),
})

// Routes that require authentication
const protectedRoutes = [
  '/profile',
  '/dashboard',
  '/settings'
]

// Routes that should redirect to dashboard if already authenticated
const guestOnlyRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password'
]

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Initialize auth store if not already initialized
  if (!authStore.initialized) {
    await authStore.initializeAuth()
  }
  
  const isAuthenticated = authStore.isAuthenticated
  const isProtectedRoute = protectedRoutes.some(route => to.path.startsWith(route))
  const isGuestOnlyRoute = guestOnlyRoutes.some(route => to.path.startsWith(route))
  
  // Redirect authenticated users away from guest-only routes
  if (isAuthenticated && isGuestOnlyRoute) {
    next('/dashboard')
    return
  }
  
  // Redirect unauthenticated users away from protected routes
  if (!isAuthenticated && isProtectedRoute) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // Allow navigation
  next()
})

// Workaround for https://github.com/vitejs/vite/issues/11804
router.onError((err, to) => {
  if (err?.message?.includes?.('Failed to fetch dynamically imported module')) {
    if (localStorage.getItem('vuetify:dynamic-reload')) {
      console.error('Dynamic import error, reloading page did not fix it', err)
    } else {
      console.log('Reloading page to fix dynamic import error')
      localStorage.setItem('vuetify:dynamic-reload', 'true')
      location.assign(to.fullPath)
    }
  } else {
    console.error(err)
  }
})

router.isReady().then(() => {
  localStorage.removeItem('vuetify:dynamic-reload')
})

export default router
