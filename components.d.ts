/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./ui/components/AppFooter.vue')['default']
    DashboardStats: typeof import('./ui/components/dashboard/DashboardStats.vue')['default']
    HelloWorld: typeof import('./ui/components/HelloWorld.vue')['default']
    MigrationManager: typeof import('./ui/components/migration/MigrationManager.vue')['default']
    ProjectCard: typeof import('./ui/components/projects/ProjectCard.vue')['default']
    ProjectManager: typeof import('./ui/components/projects/ProjectManager.vue')['default']
    ProjectSelector: typeof import('./ui/components/projects/ProjectSelector.vue')['default']
    RefactoredProjectCard: typeof import('./ui/components/examples/RefactoredProjectCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TaskFilters: typeof import('./ui/components/TaskFilters.vue')['default']
    TaskForm: typeof import('./ui/components/tasks/TaskForm.vue')['default']
    TaskGridItem: typeof import('./ui/components/tasks/TaskGridItem.vue')['default']
    TaskListItem: typeof import('./ui/components/tasks/TaskListItem.vue')['default']
    TaskStatistics: typeof import('./ui/components/TaskStatistics.vue')['default']
    TaskUploadInfo: typeof import('./ui/components/TaskUploadInfo.vue')['default']
  }
}
