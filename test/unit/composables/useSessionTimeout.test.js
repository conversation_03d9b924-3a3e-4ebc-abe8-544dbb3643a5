/**
 * Session Timeout Composable Unit Tests
 * Tests for session timeout detection and warning functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { nextTick } from 'vue'
import { createPinia, setActivePinia } from 'pinia'
import { useSessionTimeout } from '../../../ui/composables/useSessionTimeout.js'

// Mock dependencies
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn()
  })
}))

vi.mock('../../../ui/stores/auth.js', () => ({
  useAuthStore: () => ({
    isAuthenticated: true,
    token: 'test-token',
    logout: vi.fn(),
    $subscribe: vi.fn()
  })
}))

// Mock fetch
global.fetch = vi.fn()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock document.addEventListener
const eventListeners = new Map()
const originalAddEventListener = document.addEventListener
const originalRemoveEventListener = document.removeEventListener

document.addEventListener = vi.fn((event, handler, options) => {
  if (!eventListeners.has(event)) {
    eventListeners.set(event, [])
  }
  eventListeners.get(event).push({ handler, options })
})

document.removeEventListener = vi.fn((event, handler) => {
  if (eventListeners.has(event)) {
    const listeners = eventListeners.get(event)
    const index = listeners.findIndex(l => l.handler === handler)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }
})

describe('useSessionTimeout', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    // Clear all mocks
    vi.clearAllMocks()
    eventListeners.clear()
    
    // Reset timers
    vi.useFakeTimers()
    
    // Mock successful fetch responses
    global.fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true })
    })
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.resetAllMocks()
    
    // Restore original event listeners
    document.addEventListener = originalAddEventListener
    document.removeEventListener = originalRemoveEventListener
  })

  describe('initialization', () => {
    it('should initialize with default configuration', () => {
      const {
        showWarning,
        timeRemaining,
        isActive,
        timeUntilWarning,
        timeUntilLogout
      } = useSessionTimeout()

      expect(showWarning.value).toBe(false)
      expect(timeRemaining.value).toBe(0)
      expect(isActive.value).toBe(false) // Not started yet
      expect(timeUntilWarning.value).toBeGreaterThan(0)
      expect(timeUntilLogout.value).toBeGreaterThan(0)
    })

    it('should accept custom configuration', () => {
      const customConfig = {
        TIMEOUT_DURATION: 60 * 60 * 1000, // 1 hour
        WARNING_THRESHOLD: 10 * 60 * 1000, // 10 minutes
        CHECK_INTERVAL: 60 * 1000 // 1 minute
      }

      const { timeUntilWarning } = useSessionTimeout(customConfig)

      // Should use custom timeout duration
      expect(timeUntilWarning.value).toBeGreaterThan(50 * 60 * 1000) // More than 50 minutes
    })
  })

  describe('activity tracking', () => {
    it('should register activity event listeners', () => {
      const { startMonitoring } = useSessionTimeout()
      
      startMonitoring()

      // Check that activity events are registered
      const expectedEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
      expectedEvents.forEach(event => {
        expect(eventListeners.has(event)).toBe(true)
        expect(eventListeners.get(event).length).toBeGreaterThan(0)
      })
    })

    it('should update activity timestamp on user activity', () => {
      const { startMonitoring, updateActivity } = useSessionTimeout()
      
      startMonitoring()
      
      const initialTime = Date.now()
      vi.advanceTimersByTime(5000) // Advance 5 seconds
      
      updateActivity()
      
      // Activity should be updated to current time
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'session_activity',
        expect.any(String)
      )
    })

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      const { updateActivity } = useSessionTimeout()
      
      expect(() => updateActivity()).not.toThrow()
    })
  })

  describe('warning system', () => {
    it('should show warning when approaching timeout', () => {
      const config = {
        TIMEOUT_DURATION: 10000, // 10 seconds
        WARNING_THRESHOLD: 5000   // 5 seconds
      }

      const { startMonitoring, showWarning } = useSessionTimeout(config)
      
      startMonitoring()
      
      // Advance time to trigger warning
      vi.advanceTimersByTime(6000) // 6 seconds (past warning threshold)
      
      expect(showWarning.value).toBe(true)
    })

    it('should hide warning when activity is detected', () => {
      const config = {
        TIMEOUT_DURATION: 10000,
        WARNING_THRESHOLD: 5000
      }

      const { startMonitoring, showWarning, updateActivity } = useSessionTimeout(config)
      
      startMonitoring()
      
      // Trigger warning
      vi.advanceTimersByTime(6000)
      expect(showWarning.value).toBe(true)
      
      // Simulate user activity
      updateActivity()
      
      expect(showWarning.value).toBe(false)
    })

    it('should format time remaining correctly', () => {
      const { formattedTimeRemaining } = useSessionTimeout()
      
      // Mock timeRemaining to test formatting
      const testCases = [
        { ms: 300000, expected: '5:00' },  // 5 minutes
        { ms: 90000, expected: '1:30' },   // 1 minute 30 seconds
        { ms: 30000, expected: '0:30' },   // 30 seconds
        { ms: 5000, expected: '0:05' }     // 5 seconds
      ]

      testCases.forEach(({ ms, expected }) => {
        // This would require mocking the internal timeRemaining ref
        // In a real test, we'd need to trigger the countdown
        expect(typeof formattedTimeRemaining.value).toBe('string')
      })
    })
  })

  describe('session extension', () => {
    it('should extend session successfully', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      const { extendSession } = useSessionTimeout()
      
      const result = await extendSession()
      
      expect(result.success).toBe(true)
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/extend-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      })
    })

    it('should handle session extension failure', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      })

      const { extendSession } = useSessionTimeout()
      
      const result = await extendSession()
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })

    it('should handle network errors during extension', async () => {
      global.fetch.mockRejectedValueOnce(new Error('Network error'))

      const { extendSession } = useSessionTimeout()
      
      const result = await extendSession()
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('Network error')
    })
  })

  describe('session timeout handling', () => {
    it('should logout user when session times out', async () => {
      const mockAuthStore = {
        isAuthenticated: true,
        token: 'test-token',
        logout: vi.fn(),
        $subscribe: vi.fn()
      }

      const mockRouter = {
        push: vi.fn()
      }

      // Mock the stores
      vi.doMock('../../../ui/stores/auth.js', () => ({
        useAuthStore: () => mockAuthStore
      }))

      vi.doMock('vue-router', () => ({
        useRouter: () => mockRouter
      }))

      const { handleSessionTimeout } = useSessionTimeout()
      
      await handleSessionTimeout()
      
      expect(mockAuthStore.logout).toHaveBeenCalled()
      expect(mockRouter.push).toHaveBeenCalledWith({
        name: 'login',
        query: { reason: 'timeout' }
      })
    })

    it('should redirect to login even if logout fails', async () => {
      const mockAuthStore = {
        isAuthenticated: true,
        token: 'test-token',
        logout: vi.fn().mockRejectedValue(new Error('Logout failed')),
        $subscribe: vi.fn()
      }

      const mockRouter = {
        push: vi.fn()
      }

      vi.doMock('../../../ui/stores/auth.js', () => ({
        useAuthStore: () => mockAuthStore
      }))

      vi.doMock('vue-router', () => ({
        useRouter: () => mockRouter
      }))

      const { handleSessionTimeout } = useSessionTimeout()
      
      await handleSessionTimeout()
      
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' })
    })
  })

  describe('cross-tab communication', () => {
    it('should check for activity in other tabs', () => {
      const { startMonitoring } = useSessionTimeout()
      
      // Mock activity in localStorage
      localStorageMock.getItem.mockReturnValue(String(Date.now() + 5000))
      
      startMonitoring()
      
      // Advance time to trigger cross-tab check
      vi.advanceTimersByTime(30000) // 30 seconds
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('session_activity')
    })

    it('should handle localStorage errors during cross-tab check', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      const { startMonitoring } = useSessionTimeout()
      
      expect(() => startMonitoring()).not.toThrow()
    })
  })

  describe('cleanup', () => {
    it('should remove event listeners when stopped', () => {
      const { startMonitoring, stopMonitoring } = useSessionTimeout()
      
      startMonitoring()
      stopMonitoring()
      
      // Check that removeEventListener was called for each activity event
      const expectedEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
      expect(document.removeEventListener).toHaveBeenCalledTimes(expectedEvents.length)
    })

    it('should clear timers when stopped', () => {
      const { startMonitoring, stopMonitoring, isActive } = useSessionTimeout()
      
      startMonitoring()
      expect(isActive.value).toBe(true)
      
      stopMonitoring()
      expect(isActive.value).toBe(false)
    })
  })

  describe('session status', () => {
    it('should provide comprehensive session status', () => {
      const { getSessionStatus, startMonitoring } = useSessionTimeout()
      
      startMonitoring()
      const status = getSessionStatus()
      
      expect(status).toHaveProperty('isActive')
      expect(status).toHaveProperty('lastActivity')
      expect(status).toHaveProperty('timeUntilWarning')
      expect(status).toHaveProperty('timeUntilLogout')
      expect(status).toHaveProperty('showWarning')
      expect(status).toHaveProperty('timeRemaining')
      expect(status).toHaveProperty('formattedTimeRemaining')
      
      expect(typeof status.isActive).toBe('boolean')
      expect(typeof status.lastActivity).toBe('number')
      expect(typeof status.timeUntilWarning).toBe('number')
      expect(typeof status.timeUntilLogout).toBe('number')
      expect(typeof status.showWarning).toBe('boolean')
      expect(typeof status.timeRemaining).toBe('number')
      expect(typeof status.formattedTimeRemaining).toBe('string')
    })
  })

  describe('edge cases', () => {
    it('should handle unauthenticated state gracefully', () => {
      const mockAuthStore = {
        isAuthenticated: false,
        token: null,
        logout: vi.fn(),
        $subscribe: vi.fn()
      }

      vi.doMock('../../../ui/stores/auth.js', () => ({
        useAuthStore: () => mockAuthStore
      }))

      const { startMonitoring, isActive } = useSessionTimeout()
      
      startMonitoring()
      
      // Should not start monitoring when not authenticated
      expect(isActive.value).toBe(false)
    })

    it('should handle multiple start/stop cycles', () => {
      const { startMonitoring, stopMonitoring, isActive } = useSessionTimeout()
      
      // Start and stop multiple times
      startMonitoring()
      expect(isActive.value).toBe(true)
      
      stopMonitoring()
      expect(isActive.value).toBe(false)
      
      startMonitoring()
      expect(isActive.value).toBe(true)
      
      stopMonitoring()
      expect(isActive.value).toBe(false)
    })
  })
})
