/**
 * Session Service Unit Tests
 * Tests for session management functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { SessionService } from '../../../api/services/sessionService.js'

describe('SessionService', () => {
  beforeEach(() => {
    // Clear any existing sessions before each test
    // Note: In a real implementation, we'd need access to the internal storage
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('createSession', () => {
    it('should create a new session with valid data', () => {
      const userId = 'user-123'
      const token = 'jwt-token-123'
      const userAgent = 'Mozilla/5.0 Test Browser'
      const ip = '***********'

      const session = SessionService.createSession(userId, token, userAgent, ip)

      expect(session).toBeDefined()
      expect(session.id).toBeDefined()
      expect(session.userId).toBe(userId)
      expect(session.token).toBe(token)
      expect(session.userAgent).toBe(userAgent)
      expect(session.ip).toBe(ip)
      expect(session.isActive).toBe(true)
      expect(session.createdAt).toBeInstanceOf(Date)
      expect(session.lastActivity).toBeInstanceOf(Date)
    })

    it('should generate unique session IDs', () => {
      const session1 = SessionService.createSession('user1', 'token1', 'browser1', 'ip1')
      const session2 = SessionService.createSession('user2', 'token2', 'browser2', 'ip2')

      expect(session1.id).not.toBe(session2.id)
    })
  })

  describe('getSession', () => {
    it('should retrieve session by ID', () => {
      const session = SessionService.createSession('user-123', 'token-123', 'browser', 'ip')
      const retrieved = SessionService.getSession(session.id)

      expect(retrieved).toBeDefined()
      expect(retrieved.id).toBe(session.id)
      expect(retrieved.userId).toBe('user-123')
    })

    it('should return null for non-existent session', () => {
      const retrieved = SessionService.getSession('non-existent-id')
      expect(retrieved).toBeNull()
    })
  })

  describe('getSessionByToken', () => {
    it('should retrieve session by token', () => {
      const token = 'unique-token-123'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      const retrieved = SessionService.getSessionByToken(token)

      expect(retrieved).toBeDefined()
      expect(retrieved.token).toBe(token)
      expect(retrieved.id).toBe(session.id)
    })

    it('should return null for non-existent token', () => {
      const retrieved = SessionService.getSessionByToken('non-existent-token')
      expect(retrieved).toBeNull()
    })
  })

  describe('getUserSessions', () => {
    it('should return all active sessions for a user', () => {
      const userId = 'user-123'
      
      // Create multiple sessions for the same user
      SessionService.createSession(userId, 'token1', 'browser1', 'ip1')
      SessionService.createSession(userId, 'token2', 'browser2', 'ip2')
      SessionService.createSession('other-user', 'token3', 'browser3', 'ip3')

      const userSessions = SessionService.getUserSessions(userId)

      expect(userSessions).toHaveLength(2)
      expect(userSessions.every(session => session.userId === userId)).toBe(true)
      expect(userSessions.every(session => session.isActive)).toBe(true)
    })

    it('should return empty array for user with no sessions', () => {
      const userSessions = SessionService.getUserSessions('non-existent-user')
      expect(userSessions).toHaveLength(0)
    })
  })

  describe('updateSessionActivity', () => {
    it('should update last activity for valid session', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      const originalActivity = session.lastActivity

      // Wait a bit to ensure timestamp difference
      setTimeout(() => {
        const updated = SessionService.updateSessionActivity(token)
        const retrievedSession = SessionService.getSessionByToken(token)

        expect(updated).toBe(true)
        expect(retrievedSession.lastActivity.getTime()).toBeGreaterThan(originalActivity.getTime())
      }, 10)
    })

    it('should return false for non-existent token', () => {
      const updated = SessionService.updateSessionActivity('non-existent-token')
      expect(updated).toBe(false)
    })

    it('should return false for inactive session', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      // Invalidate the session
      SessionService.invalidateSession(session.id)
      
      const updated = SessionService.updateSessionActivity(token)
      expect(updated).toBe(false)
    })
  })

  describe('invalidateSession', () => {
    it('should invalidate session by ID', () => {
      const session = SessionService.createSession('user-123', 'token-123', 'browser', 'ip')
      
      const invalidated = SessionService.invalidateSession(session.id)
      const retrieved = SessionService.getSession(session.id)

      expect(invalidated).toBe(true)
      expect(retrieved.isActive).toBe(false)
    })

    it('should add token to blacklist when invalidating', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      SessionService.invalidateSession(session.id)
      
      const isBlacklisted = SessionService.isTokenBlacklisted(token)
      expect(isBlacklisted).toBe(true)
    })

    it('should return false for non-existent session', () => {
      const invalidated = SessionService.invalidateSession('non-existent-id')
      expect(invalidated).toBe(false)
    })
  })

  describe('invalidateSessionByToken', () => {
    it('should invalidate session by token', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      const invalidated = SessionService.invalidateSessionByToken(token)
      const retrieved = SessionService.getSession(session.id)

      expect(invalidated).toBe(true)
      expect(retrieved.isActive).toBe(false)
      expect(SessionService.isTokenBlacklisted(token)).toBe(true)
    })

    it('should return false for non-existent token', () => {
      const invalidated = SessionService.invalidateSessionByToken('non-existent-token')
      expect(invalidated).toBe(false)
    })
  })

  describe('invalidateAllUserSessions', () => {
    it('should invalidate all sessions for a user', () => {
      const userId = 'user-123'
      
      // Create multiple sessions
      SessionService.createSession(userId, 'token1', 'browser1', 'ip1')
      SessionService.createSession(userId, 'token2', 'browser2', 'ip2')
      SessionService.createSession(userId, 'token3', 'browser3', 'ip3')

      const invalidatedCount = SessionService.invalidateAllUserSessions(userId)

      expect(invalidatedCount).toBe(3)
      
      // Check that all tokens are blacklisted
      expect(SessionService.isTokenBlacklisted('token1')).toBe(true)
      expect(SessionService.isTokenBlacklisted('token2')).toBe(true)
      expect(SessionService.isTokenBlacklisted('token3')).toBe(true)
    })

    it('should exclude specified token from invalidation', () => {
      const userId = 'user-123'
      const excludeToken = 'token2'
      
      SessionService.createSession(userId, 'token1', 'browser1', 'ip1')
      SessionService.createSession(userId, excludeToken, 'browser2', 'ip2')
      SessionService.createSession(userId, 'token3', 'browser3', 'ip3')

      const invalidatedCount = SessionService.invalidateAllUserSessions(userId, excludeToken)

      expect(invalidatedCount).toBe(2)
      expect(SessionService.isTokenBlacklisted('token1')).toBe(true)
      expect(SessionService.isTokenBlacklisted(excludeToken)).toBe(false)
      expect(SessionService.isTokenBlacklisted('token3')).toBe(true)
    })
  })

  describe('isTokenBlacklisted', () => {
    it('should return true for blacklisted tokens', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      SessionService.invalidateSession(session.id)
      
      expect(SessionService.isTokenBlacklisted(token)).toBe(true)
    })

    it('should return false for non-blacklisted tokens', () => {
      const token = 'valid-token'
      SessionService.createSession('user-123', token, 'browser', 'ip')
      
      expect(SessionService.isTokenBlacklisted(token)).toBe(false)
    })
  })

  describe('validateSession', () => {
    it('should validate active session and update activity', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      const validation = SessionService.validateSession(token)

      expect(validation.valid).toBe(true)
      expect(validation.session.id).toBe(session.id)
      expect(validation.session.userId).toBe('user-123')
    })

    it('should reject blacklisted tokens', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      SessionService.invalidateSession(session.id)
      
      const validation = SessionService.validateSession(token)

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Token is blacklisted')
    })

    it('should reject non-existent sessions', () => {
      const validation = SessionService.validateSession('non-existent-token')

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Session not found')
    })

    it('should reject inactive sessions', () => {
      const token = 'test-token'
      const session = SessionService.createSession('user-123', token, 'browser', 'ip')
      
      // Manually set session as inactive (without blacklisting)
      session.isActive = false
      
      const validation = SessionService.validateSession(token)

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Session is inactive')
    })
  })

  describe('getSessionStats', () => {
    it('should return correct session statistics', () => {
      // Create some sessions
      SessionService.createSession('user1', 'token1', 'browser1', 'ip1')
      SessionService.createSession('user1', 'token2', 'browser2', 'ip2')
      SessionService.createSession('user2', 'token3', 'browser3', 'ip3')
      
      // Invalidate one session
      SessionService.invalidateSessionByToken('token2')

      const stats = SessionService.getSessionStats()

      expect(stats.activeSessions).toBe(2)
      expect(stats.totalSessions).toBe(3)
      expect(stats.blacklistedTokens).toBeGreaterThan(0)
      expect(stats.uniqueUsers).toBe(2)
      expect(stats.averageSessionsPerUser).toBe(1) // 2 active sessions / 2 users
    })
  })

  describe('forceLogoutUser', () => {
    it('should force logout all sessions for a user', () => {
      const userId = 'user-123'
      
      SessionService.createSession(userId, 'token1', 'browser1', 'ip1')
      SessionService.createSession(userId, 'token2', 'browser2', 'ip2')

      const result = SessionService.forceLogoutUser(userId)

      expect(result.success).toBe(true)
      expect(result.sessionsInvalidated).toBe(2)
      expect(result.message).toContain(userId)
    })
  })

  describe('getUserSessionDetails', () => {
    it('should return detailed session information', () => {
      const userId = 'user-123'
      
      const session1 = SessionService.createSession(userId, 'token1', 'Mozilla/5.0', '***********')
      const session2 = SessionService.createSession(userId, 'token2', 'Chrome/90.0', '***********')

      const details = SessionService.getUserSessionDetails(userId)

      expect(details).toHaveLength(2)
      expect(details[0]).toHaveProperty('id')
      expect(details[0]).toHaveProperty('createdAt')
      expect(details[0]).toHaveProperty('lastActivity')
      expect(details[0]).toHaveProperty('userAgent')
      expect(details[0]).toHaveProperty('ip')
      expect(details[0]).toHaveProperty('isActive')
      expect(details[0]).toHaveProperty('isCurrent')
      
      expect(details[0].userAgent).toBe('Mozilla/5.0')
      expect(details[1].userAgent).toBe('Chrome/90.0')
    })
  })
})
