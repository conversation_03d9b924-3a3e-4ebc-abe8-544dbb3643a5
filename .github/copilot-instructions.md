# Custom Instructions for GitHub Copilot

- Use Vue 3 Composition API for all Vue components.
- Prefer Vuetify 3 components (e.g., `v-btn`, `v-card`) for UI elements.
- Always include TypeScript types for props, state, and computed properties.
- Use arrow functions for event handlers and computed properties.
- Follow Vite conventions for imports (e.g., `import { defineComponent } from 'vue'`).
- Use descriptive variable names (e.g., `userProfile` instead of `data`).
- Include Vuetify styles with `import 'vuetify/styles'`.
- Organize components in a `src/components` folder.
- Use single-file components with `<script setup>` syntax when possible.