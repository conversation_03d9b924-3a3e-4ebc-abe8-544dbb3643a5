# Track Tasks - Project Task Management System

A modern Vue 3 + Vuetify application for managing project tasks through JSON file uploads with local database storage.

## ✨ Features

- **JSON File Import**: Upload and import task data from JSON files
- **Local Database**: Browser-based IndexedDB storage for offline access
- **Task Management**: View, filter, search, and update task statuses
- **Modern UI**: Clean, responsive Vuetify Material Design interface
- **Real-time Statistics**: Dashboard with task analytics and insights

## 🚀 Quick Start

1. **Install Dependencies**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

3. **Open Application**
   - Navigate to `http://localhost:3000`
   - Click "Task Manager" to start uploading tasks

## 📋 Using Track Tasks

### 1. Upload JSON Tasks
- Click on "Task Manager" in the navigation
- Select a JSON file containing your tasks
- Click "Process Upload" to import into the database
- View upload results and any validation errors

### 2. Manage Tasks
- **View**: Switch between List and Grid views
- **Filter**: By type, priority, status, and epic
- **Search**: Find tasks by ID, summary, or description
- **Update**: Change task statuses with dropdown menus

### 3. Task Statistics
- View real-time statistics on the home page
- Track completion progress and task distribution
- Monitor project health and progress

## 📁 JSON File Format

Your JSON file should contain an array of task objects:

```json
[
  {
    "id": "TASK-001",
    "summary": "Task title (required)",
    "description": "Detailed description (optional)",
    "linked_tasks": ["TASK-002"],
    "epic": "Project Phase",
    "priority": "High|Medium|Low",
    "estimated_effort": "Large|Medium|Small",
    "type": "Story|Task|Epic|Bug"
  }
]
```

**Required Fields:** `id`, `summary`  
**Optional Fields:** All others have sensible defaults

## 🔧 Technical Stack

- **Frontend**: Vue 3 + Composition API
- **UI Library**: Vuetify 3 (Material Design)
- **State Management**: Pinia
- **Database**: IndexedDB (browser-based)
- **Build Tool**: Vite
- **Routing**: Vue Router

## 📖 Documentation

- See `TASK_MANAGER_README.md` for detailed usage instructions
- Example files available in `public/example-tasks.json`
- Sample data in `data/backlog.json`

## 📄 License

MIT License - Built with Vue 3 and Vuetify
