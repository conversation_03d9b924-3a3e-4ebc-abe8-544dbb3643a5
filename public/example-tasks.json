[{"id": "EXAMPLE-001", "summary": "Set up project repository", "description": "Initialize a new project repository with proper folder structure, README, and initial configuration files.", "linked_tasks": [], "epic": "Project Setup", "priority": "High", "estimated_effort": "Small", "type": "Task"}, {"id": "EXAMPLE-002", "summary": "Design user interface mockups", "description": "Create wireframes and mockups for the main user interface screens including dashboard, forms, and navigation.", "linked_tasks": ["EXAMPLE-001"], "epic": "UI/UX Design", "priority": "Medium", "estimated_effort": "Large", "type": "Story"}, {"id": "EXAMPLE-003", "summary": "Implement user authentication", "description": "Build user registration, login, and authentication system with proper security measures.", "linked_tasks": ["EXAMPLE-002"], "epic": "Authentication", "priority": "High", "estimated_effort": "Medium", "type": "Story"}, {"id": "EXAMPLE-004", "summary": "Fix login validation bug", "description": "Resolve issue where users can login with invalid credentials under certain conditions.", "linked_tasks": ["EXAMPLE-003"], "epic": "Authentication", "priority": "High", "estimated_effort": "Small", "type": "Bug"}, {"id": "EXAMPLE-005", "summary": "Create user dashboard", "description": "Build the main dashboard interface where users can view their data and access key features.", "linked_tasks": ["EXAMPLE-002", "EXAMPLE-003"], "epic": "Dashboard", "priority": "Medium", "estimated_effort": "Medium", "type": "Story"}, {"id": "EXAMPLE-006", "summary": "Mobile application epic", "description": "Parent epic for all mobile application development tasks including iOS and Android versions.", "linked_tasks": [], "epic": "Mobile App", "priority": "Low", "estimated_effort": "Large", "type": "Epic"}]