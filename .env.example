# Track Tasks API Configuration

# API Server Configuration
API_PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration (PocketBase)
VITE_POCKETBASE_URL=http://localhost:8090
VITE_POCKETBASE_EMAIL=<EMAIL>
VITE_POCKETBASE_PASSWORD=your-pocketbase-password

# Security Configuration
JWT_SECRET=your-jwt-secret-key-change-in-production
API_KEY=your-api-key-for-programmatic-access

# Development/Testing
DEBUG=true
LOG_LEVEL=info
