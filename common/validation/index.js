/**
 * Shared Validation Utilities
 * Used by both API and UI modules
 */

import {
  TASK_PRIORITY_VALUES,
  TASK_TYPE_VALUES,
  TASK_STATUS_VALUES,
  TASK_ESTIMATED_EFFORT_VALUES,
  TASK_ID_PATTERN,
  TASK_ID_PATTERN_FLEXIBLE,
  VALIDATION_CONSTRAINTS
} from '../constants/index.js'

/**
 * Sanitize and normalize data
 * @param {Object} data - Data to sanitize
 * @returns {Object} Sanitized data
 */
export function sanitizeData(data) {
  const sanitized = {}
  
  for (const [key, value] of Object.entries(data)) {
    if (value === null || value === undefined) {
      sanitized[key] = null
    } else if (typeof value === 'string') {
      sanitized[key] = value.trim()
    } else {
      sanitized[key] = value
    }
  }
  
  return sanitized
}

/**
 * Validate task ID format (strict API format)
 * @param {string} taskId - Task ID to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskId(taskId) {
  return TASK_ID_PATTERN.test(taskId)
}

/**
 * Validate task ID format (flexible format)
 * @param {string} taskId - Task ID to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskIdFlexible(taskId) {
  return TASK_ID_PATTERN_FLEXIBLE.test(taskId)
}

/**
 * Validate UUID format
 * @param {string} uuid - UUID to validate
 * @returns {boolean} True if valid
 */
export function isValidUUID(uuid) {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uuid)
}

/**
 * Validate task priority
 * @param {string} priority - Priority to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskPriority(priority) {
  return TASK_PRIORITY_VALUES.includes(priority)
}

/**
 * Validate task type
 * @param {string} type - Type to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskType(type) {
  return TASK_TYPE_VALUES.includes(type)
}

/**
 * Validate task status
 * @param {string} status - Status to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskStatus(status) {
  return TASK_STATUS_VALUES.includes(status)
}

/**
 * Validate task estimated effort
 * @param {string} effort - Effort to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskEstimatedEffort(effort) {
  return TASK_ESTIMATED_EFFORT_VALUES.includes(effort)
}

/**
 * Validate string length
 * @param {string} value - Value to validate
 * @param {number} min - Minimum length
 * @param {number} max - Maximum length
 * @returns {boolean} True if valid
 */
export function isValidStringLength(value, min = 0, max = Infinity) {
  if (typeof value !== 'string') return false
  const trimmed = value.trim()
  return trimmed.length >= min && trimmed.length <= max
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid
 */
export function isValidEmail(email) {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailPattern.test(email)
}

/**
 * Comprehensive task validation
 * @param {Object} task - Task object to validate
 * @param {Array} existingTasks - Array of existing tasks for uniqueness check
 * @returns {Object} Validation result with errors
 */
export function validateTask(task, existingTasks = []) {
  const errors = []
  
  // Validate required fields
  if (!task.summary || !isValidStringLength(task.summary, VALIDATION_CONSTRAINTS.TASK_SUMMARY_MIN_LENGTH, VALIDATION_CONSTRAINTS.TASK_SUMMARY_MAX_LENGTH)) {
    errors.push(`Summary must be between ${VALIDATION_CONSTRAINTS.TASK_SUMMARY_MIN_LENGTH} and ${VALIDATION_CONSTRAINTS.TASK_SUMMARY_MAX_LENGTH} characters`)
  }
  
  if (!task.project_id || !task.project_id.trim()) {
    errors.push('Project ID is required')
  }
  
  // Validate optional fields
  if (task.description && !isValidStringLength(task.description, 0, VALIDATION_CONSTRAINTS.TASK_DESCRIPTION_MAX_LENGTH)) {
    errors.push(`Description must be less than ${VALIDATION_CONSTRAINTS.TASK_DESCRIPTION_MAX_LENGTH} characters`)
  }
  
  if (task.priority && !isValidTaskPriority(task.priority)) {
    errors.push(`Priority must be one of: ${TASK_PRIORITY_VALUES.join(', ')}`)
  }
  
  if (task.type && !isValidTaskType(task.type)) {
    errors.push(`Type must be one of: ${TASK_TYPE_VALUES.join(', ')}`)
  }
  
  if (task.status && !isValidTaskStatus(task.status)) {
    errors.push(`Status must be one of: ${TASK_STATUS_VALUES.join(', ')}`)
  }
  
  if (task.estimated_effort && !isValidTaskEstimatedEffort(task.estimated_effort)) {
    errors.push(`Estimated effort must be one of: ${TASK_ESTIMATED_EFFORT_VALUES.join(', ')}`)
  }
  
  if (task.epic && !isValidStringLength(task.epic, 0, VALIDATION_CONSTRAINTS.TASK_EPIC_MAX_LENGTH)) {
    errors.push(`Epic must be less than ${VALIDATION_CONSTRAINTS.TASK_EPIC_MAX_LENGTH} characters`)
  }
  
  if (task.assigned_to && !isValidStringLength(task.assigned_to, 0, VALIDATION_CONSTRAINTS.TASK_ASSIGNED_TO_MAX_LENGTH)) {
    errors.push(`Assigned to must be less than ${VALIDATION_CONSTRAINTS.TASK_ASSIGNED_TO_MAX_LENGTH} characters`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Comprehensive project validation
 * @param {Object} project - Project object to validate
 * @returns {Object} Validation result with errors
 */
export function validateProject(project) {
  const errors = []
  
  // Validate required fields
  if (!project.name || !isValidStringLength(project.name, VALIDATION_CONSTRAINTS.PROJECT_NAME_MIN_LENGTH, VALIDATION_CONSTRAINTS.PROJECT_NAME_MAX_LENGTH)) {
    errors.push(`Project name must be between ${VALIDATION_CONSTRAINTS.PROJECT_NAME_MIN_LENGTH} and ${VALIDATION_CONSTRAINTS.PROJECT_NAME_MAX_LENGTH} characters`)
  }
  
  // Validate optional fields
  if (project.description && !isValidStringLength(project.description, 0, VALIDATION_CONSTRAINTS.PROJECT_DESCRIPTION_MAX_LENGTH)) {
    errors.push(`Project description must be less than ${VALIDATION_CONSTRAINTS.PROJECT_DESCRIPTION_MAX_LENGTH} characters`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate pagination parameters
 * @param {number} limit - Limit parameter
 * @param {number} offset - Offset parameter
 * @returns {Object} Validation result with errors
 */
export function validatePagination(limit, offset) {
  const errors = []
  
  if (limit !== undefined && (typeof limit !== 'number' || limit < 1 || limit > 1000)) {
    errors.push('Limit must be a number between 1 and 1000')
  }
  
  if (offset !== undefined && (typeof offset !== 'number' || offset < 0)) {
    errors.push('Offset must be a non-negative number')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate search query
 * @param {string} query - Search query
 * @returns {Object} Validation result with errors
 */
export function validateSearchQuery(query) {
  const errors = []
  
  if (!query || !isValidStringLength(query, VALIDATION_CONSTRAINTS.SEARCH_QUERY_MIN_LENGTH, VALIDATION_CONSTRAINTS.SEARCH_QUERY_MAX_LENGTH)) {
    errors.push(`Search query must be between ${VALIDATION_CONSTRAINTS.SEARCH_QUERY_MIN_LENGTH} and ${VALIDATION_CONSTRAINTS.SEARCH_QUERY_MAX_LENGTH} characters`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// Re-export task validation utilities
export {
  validateTaskIdFormat,
  checkTaskIdExists,
  validateTaskId,
  validateLinkedTask,
  generateTaskId
} from './taskValidation.js'
