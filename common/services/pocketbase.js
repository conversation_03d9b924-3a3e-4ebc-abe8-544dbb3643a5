import PocketBase from 'pocketbase'

/**
 * PocketBase client instance
 * Reads connection configuration from environment variables
 */
class PocketBaseService {
  constructor() {
    // Get PocketBase URL from environment variables, default to localhost
    const pbUrl = import.meta.env.VITE_POCKETBASE_URL || 'http://localhost:8090'
    
    // Initialize PocketBase client
    this.pb = new PocketBase(pbUrl)
    
    // Enable auto cancellation for duplicate requests
    this.pb.autoCancellation = true
    
    // Load auth state from localStorage if available
    this.pb.authStore.loadFromCookie(document.cookie)
  }

  /**
   * Get the PocketBase client instance
   * @returns {PocketBase} The PocketBase client
   */
  getClient() {
    return this.pb
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} True if user is authenticated
   */
  isAuthenticated() {
    return this.pb.authStore.isValid
  }

  /**
   * Get current user data
   * @returns {Object|null} User data or null if not authenticated
   */
  getCurrentUser() {
    return this.pb.authStore.model
  }

  /**
   * Authenticate user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Authentication result
   */
  async login(email, password) {
    try {
      const authData = await this.pb.collection('users').authWithPassword(email, password)
      return { success: true, user: authData.record }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration result
   */
  async register(userData) {
    try {
      const user = await this.pb.collection('users').create(userData)
      return { success: true, user }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Logout current user
   */
  logout() {
    this.pb.authStore.clear()
  }

  /**
   * Request password reset email
   * @param {string} email - User email to send reset link to
   * @returns {Promise<Object>} Result of password reset request
   */
  async requestPasswordReset(email) {
    try {
      await this.pb.collection('users').requestPasswordReset(email)
      return { success: true, message: 'Password reset email sent successfully' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Confirm password reset with token
   * @param {string} token - Password reset token from email
   * @param {string} password - New password
   * @param {string} passwordConfirm - Password confirmation
   * @returns {Promise<Object>} Result of password reset confirmation
   */
  async confirmPasswordReset(token, password, passwordConfirm) {
    try {
      await this.pb.collection('users').confirmPasswordReset(token, password, passwordConfirm)
      return { success: true, message: 'Password reset successfully' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Result of profile update
   */
  async updateUserProfile(userId, profileData) {
    try {
      const user = await this.pb.collection('users').update(userId, profileData)
      return { success: true, user }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Change user password
   * @param {string} userId - User ID
   * @param {string} oldPassword - Current password
   * @param {string} newPassword - New password
   * @param {string} passwordConfirm - Password confirmation
   * @returns {Promise<Object>} Result of password change
   */
  async changePassword(userId, oldPassword, newPassword, passwordConfirm) {
    try {
      const user = await this.pb.collection('users').update(userId, {
        oldPassword,
        password: newPassword,
        passwordConfirm
      })
      return { success: true, user }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Get collection reference
   * @param {string} name - Collection name
   * @returns {Object} Collection reference
   */
  collection(name) {
    return this.pb.collection(name)
  }

  /**
   * Subscribe to real-time updates
   * @param {string} topic - Topic to subscribe to
   * @param {Function} callback - Callback function for updates
   */
  subscribe(topic, callback) {
    return this.pb.subscribe(topic, callback)
  }

  /**
   * Unsubscribe from real-time updates
   * @param {string} topic - Topic to unsubscribe from
   */
  unsubscribe(topic) {
    this.pb.unsubscribe(topic)
  }
}

// Create and export a singleton instance
const pocketBaseService = new PocketBaseService()

export default pocketBaseService
export { pocketBaseService }
