/**
 * Shared Constants
 * Constants used across both API and UI modules
 */

// Task-related constants
export const TASK_PRIORITIES = {
  LOW: 'Low',
  MEDIUM: 'Medium',
  HIGH: 'High'
}

export const TASK_TYPES = {
  TASK: 'Task',
  STORY: 'Story',
  EPIC: 'Epic',
  BUG: 'Bug'
}

export const TASK_STATUSES = {
  BACKLOG: 'Backlog',
  IN_PROGRESS: 'In Progress',
  DONE: 'Done',
  BLOCKED: 'Blocked'
}

export const TASK_ESTIMATED_EFFORTS = {
  SMALL: 'Small',
  MEDIUM: 'Medium',
  LARGE: 'Large'
}

// Arrays for validation and iteration
export const TASK_PRIORITY_VALUES = Object.values(TASK_PRIORITIES)
export const TASK_TYPE_VALUES = Object.values(TASK_TYPES)
export const TASK_STATUS_VALUES = Object.values(TASK_STATUSES)
export const TASK_ESTIMATED_EFFORT_VALUES = Object.values(TASK_ESTIMATED_EFFORTS)

// Task ID patterns
export const TASK_ID_PATTERN = /^[A-Z]{2,4}-[0-9]{1,4}(-[0-9]{1,2})?$/
export const TASK_ID_PATTERN_FLEXIBLE = /^[A-Za-z0-9]$|^[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9]$/

// Validation constraints
export const VALIDATION_CONSTRAINTS = {
  TASK_ID_MAX_LENGTH: 50,
  TASK_SUMMARY_MIN_LENGTH: 1,
  TASK_SUMMARY_MAX_LENGTH: 500,
  TASK_DESCRIPTION_MAX_LENGTH: 5000,
  TASK_EPIC_MAX_LENGTH: 100,
  TASK_ASSIGNED_TO_MAX_LENGTH: 100,
  
  PROJECT_NAME_MIN_LENGTH: 1,
  PROJECT_NAME_MAX_LENGTH: 100,
  PROJECT_DESCRIPTION_MAX_LENGTH: 1000,
  
  SEARCH_QUERY_MIN_LENGTH: 1,
  SEARCH_QUERY_MAX_LENGTH: 200
}

// Database collection names
export const COLLECTIONS = {
  TASKS: 'tasks',
  PROJECTS: 'projects'
}

// Link types for task relationships
export const TASK_LINK_TYPES = {
  PARENT: 'Parent',
  REQUIRES: 'Requires'
}

// Default values
export const DEFAULTS = {
  TASK_PRIORITY: TASK_PRIORITIES.MEDIUM,
  TASK_TYPE: TASK_TYPES.TASK,
  TASK_STATUS: TASK_STATUSES.BACKLOG,
  TASK_ESTIMATED_EFFORT: TASK_ESTIMATED_EFFORTS.MEDIUM,
  PROJECT_DESCRIPTION: ''
}

// API Configuration
export const API_CONFIG = {
  DEFAULT_PAGINATION_LIMIT: 50,
  MAX_PAGINATION_LIMIT: 1000,
  DEFAULT_PAGINATION_OFFSET: 0,
  MAX_BULK_OPERATIONS: 100,
  MAX_BULK_IMPORT: 1000
}

// Environment variable keys
export const ENV_KEYS = {
  POCKETBASE_URL: 'VITE_POCKETBASE_URL',
  POCKETBASE_EMAIL: 'VITE_POCKETBASE_EMAIL',
  POCKETBASE_PASSWORD: 'VITE_POCKETBASE_PASSWORD',
  API_PORT: 'API_PORT',
  NODE_ENV: 'NODE_ENV',
  FRONTEND_URL: 'FRONTEND_URL',
  JWT_SECRET: 'JWT_SECRET'
}

// Common error codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  CONFLICT: 'CONFLICT',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  UNPROCESSABLE_ENTITY: 'UNPROCESSABLE_ENTITY'
}

// File and request size limits
export const SIZE_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_JSON_SIZE: 1 * 1024 * 1024   // 1MB
}
