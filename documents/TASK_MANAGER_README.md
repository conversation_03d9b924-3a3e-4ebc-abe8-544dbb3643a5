# Task Manager

A Vue 3 + Vuetify application for managing project tasks from JSON files using local IndexedDB storage.

## Features

- **JSON File Upload**: Upload task data from JSON files into a local database
- **Task Management**: View, filter, and update task statuses
- **Local Storage**: Uses browser's IndexedDB for client-side data persistence
- **Real-time Statistics**: View task counts by type, priority, and status
- **Search & Filter**: Search tasks and filter by type, priority, status, and epic
- **Responsive Design**: Modern Vuetify UI with mobile-friendly layout

## Getting Started

1. **Start the Development Server**
   ```bash
   npm run dev
   ```

2. **Navigate to Task Manager**
   - Open your browser to `http://localhost:3000`
   - Click on "Task Manager" in the navigation or "Get Started" button

3. **Upload Tasks**
   - Click "Select JSON file" and choose a valid JSON file
   - Click "Process Upload" to import tasks into the database
   - View upload results and any validation errors

## JSON File Format

Your JSON file should contain an array of task objects with the following structure:

```json
[
  {
    "id": "TASK-001",
    "summary": "Task title (required)",
    "description": "Detailed task description (optional)",
    "linked_tasks": ["TASK-002", "TASK-003"],
    "epic": "Epic Name",
    "priority": "High|Medium|Low",
    "estimated_effort": "Large|Medium|Small", 
    "type": "Story|Task|Epic|Bug"
  }
]
```

### Required Fields
- `id`: Unique task identifier
- `summary`: Task title/summary

### Optional Fields
- `description`: Detailed task description
- `linked_tasks`: Array of related task IDs
- `epic`: Epic or project category
- `priority`: Task priority (defaults to "Medium")
- `estimated_effort`: Size estimate (defaults to "Medium")
- `type`: Task type (defaults to "Task")

## Example Usage

1. **Test with Sample Data**
   - Download the example file: `/public/example-tasks.json`
   - Upload this file to see the system in action

2. **Use Your Backlog**
   - Use the provided `data/backlog.json` file as a comprehensive example
   - This contains real project tasks with various types and priorities

## Task Management

### Task Status
Tasks can have the following statuses:
- **Backlog**: Not yet started (default for imported tasks)
- **In Progress**: Currently being worked on
- **Done**: Completed
- **Blocked**: Cannot proceed due to dependencies

### Views
- **List View**: Compact list showing all task details
- **Grid View**: Card-based layout for better visual scanning

### Filtering
Filter tasks by:
- **Type**: Story, Task, Epic, Bug
- **Priority**: High, Medium, Low
- **Status**: Backlog, In Progress, Done, Blocked
- **Epic**: Group related tasks by epic/project

### Search
Use the search box to find tasks by:
- Task ID
- Summary text
- Description content
- Epic name

## Database Management

### Local Storage
- Tasks are stored in your browser's IndexedDB
- Data persists between browser sessions
- No external server required

### Clear Database
- Use the "Clear Database" button to remove all tasks
- Confirmation dialog prevents accidental deletion
- This action cannot be undone

## Technical Details

### Technologies Used
- **Vue 3**: Progressive JavaScript framework
- **Vuetify 3**: Material Design component library
- **Pinia**: State management
- **IndexedDB**: Browser database for local storage
- **Vite**: Build tool and development server

### Browser Compatibility
- Modern browsers supporting IndexedDB
- Chrome, Firefox, Safari, Edge (recent versions)

## Development

### Project Structure
```
src/
├── components/          # Reusable Vue components
├── layouts/            # Page layouts
├── pages/              # Route pages
│   ├── index.vue       # Home page
│   └── tasks.vue       # Task Manager page
├── services/           # Business logic
│   └── databaseService.js  # IndexedDB operations
└── stores/             # Pinia state management
    └── tasks.js        # Task management store
```

### Key Components
- **TaskManager Page**: Main task management interface
- **Database Service**: IndexedDB operations and data validation
- **Tasks Store**: Pinia store for state management and API calls

## Troubleshooting

### Upload Issues
- **Invalid JSON**: Ensure your file contains valid JSON syntax
- **Missing Required Fields**: All tasks must have `id` and `summary`
- **Large Files**: Browser may have memory limits for very large files

### Browser Storage
- **Storage Quota**: Browsers limit IndexedDB storage per domain
- **Incognito Mode**: Data won't persist in private browsing
- **Storage Full**: Clear other website data if storage is full

### Performance
- **Large Datasets**: Consider pagination for thousands of tasks
- **Search Speed**: Complex filters may be slower on large datasets

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
