# Track Tasks - Testing Guide

## 🧪 Complete Testing Checklist

### 1. Application Startup
- [ ] Start development server: `npm run dev`
- [ ] Application loads without errors
- [ ] Navigation drawer works on mobile
- [ ] Desktop navigation bar displays correctly

### 2. Home Page (`/`) - Task List
- [ ] **Empty State**
  - [ ] Shows "No Tasks Yet" message when no tasks
  - [ ] "Import Tasks" button navigates to upload page
- [ ] **With Tasks**
  - [ ] Statistics cards show correct counts
  - [ ] Task filters component loads
  - [ ] Tasks display in list/grid view
  - [ ] View mode toggle works

### 3. Upload Page (`/upload`) - Task Import
- [ ] **File Upload**
  - [ ] File input accepts .json files
  - [ ] Upload progress indicator works
  - [ ] Validation errors display correctly
  - [ ] Success message appears after import
- [ ] **Statistics Display**
  - [ ] Task count cards update after import
  - [ ] "View Tasks" button navigates to home
- [ ] **Database Management**
  - [ ] Clear database button works
  - [ ] Confirmation dialog prevents accidental deletion

### 4. Help Page (`/help`) - Documentation
- [ ] **Getting Started**
  - [ ] Step-by-step guide displays
  - [ ] Navigation buttons work
  - [ ] Links to other pages function
- [ ] **JSON Format**
  - [ ] Example code displays correctly
  - [ ] Download example button works
  - [ ] Required/optional fields explained
- [ ] **Features & Troubleshooting**
  - [ ] Feature cards display with icons
  - [ ] FAQ expands/collapses
  - [ ] Quick actions sidebar works

### 5. Task Details (`/tasks/[id]`) - Individual Tasks
- [ ] **Navigation**
  - [ ] Clicking tasks opens detail page
  - [ ] Back button returns to home
  - [ ] URL matches task ID
- [ ] **Task Information**
  - [ ] All task metadata displays
  - [ ] Status can be updated
  - [ ] Linked tasks are clickable
- [ ] **Markdown Rendering**
  - [ ] Description renders as markdown
  - [ ] View mode toggle works (rendered/source)
  - [ ] Formatting displays correctly

### 6. Enhanced Filtering System
- [ ] **Search Functionality**
  - [ ] Search box finds tasks by ID, summary, description
  - [ ] Result count updates in real-time
  - [ ] Clear search button works
- [ ] **Quick Filter Chips**
  - [ ] Status chips show task counts
  - [ ] Clicking chips filters tasks
  - [ ] Multiple chips can be selected
- [ ] **Advanced Filters**
  - [ ] Type, priority, status, epic filters work
  - [ ] Dropdown options show with icons/colors
  - [ ] Filters combine correctly
- [ ] **Filter Management**
  - [ ] Active filters display as removable chips
  - [ ] "Clear All" button resets all filters
  - [ ] Filter state persists during navigation

### 7. Navigation System
- [ ] **Mobile Navigation**
  - [ ] Hamburger menu opens drawer
  - [ ] All navigation items work
  - [ ] Task count badges display
  - [ ] Drawer closes after navigation
- [ ] **Desktop Navigation**
  - [ ] Top navigation bar shows all options
  - [ ] Active page highlighting works
  - [ ] Task count badges update
- [ ] **Route Handling**
  - [ ] Direct URL navigation works
  - [ ] Browser back/forward buttons work
  - [ ] 404 handling for invalid task IDs

### 8. Responsive Design
- [ ] **Mobile (< 768px)**
  - [ ] Navigation drawer accessible
  - [ ] Task cards stack properly
  - [ ] Filters collapse appropriately
  - [ ] Text remains readable
- [ ] **Tablet (768px - 1024px)**
  - [ ] Grid layout adjusts
  - [ ] Navigation remains accessible
  - [ ] Statistics cards resize
- [ ] **Desktop (> 1024px)**
  - [ ] Full navigation bar visible
  - [ ] Optimal grid layouts
  - [ ] All features accessible

### 9. Data Management
- [ ] **Task Storage**
  - [ ] Tasks persist between sessions
  - [ ] Status updates save correctly
  - [ ] Data survives page refresh
- [ ] **Import/Export**
  - [ ] JSON validation works
  - [ ] Error messages are helpful
  - [ ] Large files import correctly
  - [ ] Example download works

### 10. Performance & UX
- [ ] **Loading States**
  - [ ] Upload progress shows
  - [ ] Loading overlays appear
  - [ ] Skeleton states (if implemented)
- [ ] **Visual Feedback**
  - [ ] Hover effects work
  - [ ] Click animations smooth
  - [ ] Status colors consistent
  - [ ] Icons display correctly
- [ ] **Error Handling**
  - [ ] Network errors handled gracefully
  - [ ] Invalid data shows helpful messages
  - [ ] Missing tasks show 404 state

## 🎯 Test Data

### Sample JSON for Testing
```json
[
  {
    "id": "TEST-001",
    "summary": "Test task with markdown description",
    "description": "# Markdown Test\n\nThis is a **bold** test with:\n- Lists\n- `code`\n- [links](https://example.com)",
    "linked_tasks": ["TEST-002"],
    "epic": "Testing",
    "priority": "High",
    "estimated_effort": "Small",
    "type": "Story"
  },
  {
    "id": "TEST-002",
    "summary": "Linked test task",
    "description": "A simple test task",
    "linked_tasks": [],
    "epic": "Testing",
    "priority": "Medium",
    "estimated_effort": "Medium",
    "type": "Task"
  }
]
```

## 🚀 Success Criteria

The application passes testing when:
1. ✅ All navigation works smoothly
2. ✅ Task import/export functions correctly
3. ✅ Filtering and search provide expected results
4. ✅ Task detail views display and function properly
5. ✅ Responsive design works on all screen sizes
6. ✅ Data persists correctly in IndexedDB
7. ✅ Error states are handled gracefully
8. ✅ Performance is smooth and responsive

## 🐛 Common Issues to Check

1. **Route not found**: Ensure old `/tasks` route references are removed
2. **Filter not working**: Check if tasks have the expected fields
3. **Navigation broken**: Verify router setup and component imports
4. **Markdown not rendering**: Check vue3-markdown-it dependency
5. **Data not persisting**: Test IndexedDB browser support
6. **Mobile issues**: Test on actual devices, not just browser resize
