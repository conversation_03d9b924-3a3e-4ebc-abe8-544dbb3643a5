/**
 * Task Routes
 * Defines route handlers for task management API operations
 */

import express from 'express'
import { validationRules } from '../utils/validation.js'
import { authenticate, requireAdmin } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { TaskService } from '../services/taskService.js'

// Initialize new Router instance
export const taskRoutes = express.Router()

// Create new task
/**
 * @route POST /api/tasks
 */
taskRoutes.post(
  '/',
  authenticate,
  validationRules.createTask,
  asyncHandler(async (req, res) => {
    const taskData = req.body
    const task = await TaskService.createTask(taskData)
    res.status(201).json(task)
  })
)

// List all tasks with optional filtering
/**
 * @route GET /api/tasks
 */
taskRoutes.get(
  '/',
  authenticate,
  validationRules.getTasks,
  asyncHandler(async (req, res) => {
    const filters = req.query
    const tasks = await TaskService.listTasks(filters)
    res.status(200).json(tasks)
  })
)

// Get specific task by task ID
/**
 * @route GET /api/tasks/:task_id
 */
taskRoutes.get(
  '/:task_id',
  authenticate,
  validationRules.getTask,
  asyncHandler(async (req, res) => {
    const taskId = req.params.task_id
    const task = await TaskService.getTaskById(taskId)

    if (!task) {
      return res.status(404).json({ error: 'Task not found' })
    }

    res.status(200).json(task)
  })
)

// Update existing task
/**
 * @route PUT /api/tasks/:task_id
 */
taskRoutes.put(
  '/:task_id',
  authenticate,
  validationRules.updateTask,
  asyncHandler(async (req, res) => {
    const taskId = req.params.task_id
    const updates = req.body
    const updatedTask = await TaskService.updateTask(taskId, updates)

    if (!updatedTask) {
      return res.status(404).json({ error: 'Task not updated.' })
    }

    res.status(200).json(updatedTask)
  })
)

// Delete task
/**
 * @route DELETE /api/tasks/:task_id
 */
taskRoutes.delete(
  '/:task_id',
  authenticate,
  requireAdmin,
  validationRules.deleteTask,
  asyncHandler(async (req, res) => {
    const taskId = req.params.task_id
    const success = await TaskService.deleteTask(taskId)

    if (!success) {
      return res.status(404).json({ error: 'Task not deleted.' })
    }

    res.status(204).end()
  })
)

// Update task status only
/**
 * @route PATCH /api/tasks/:task_id/status
 */
taskRoutes.patch(
  '/:task_id/status',
  authenticate,
  validationRules.updateTaskStatus,
  asyncHandler(async (req, res) => {
    const taskId = req.params.task_id
    const { status } = req.body
    const updatedTask = await TaskService.updateTaskStatus(taskId, status)

    if (!updatedTask) {
      return res.status(404).json({ error: 'Task not found or status not updated.' })
    }

    res.status(200).json(updatedTask)
  })
)
