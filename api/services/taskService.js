/**
 * Task Service
 * Business logic layer for task operations
 * Integrates with existing databaseService.js
 */

import { CONFIG } from '../config/server.js'
import { errors } from '../middleware/errorHandler.js'
import { sanitizeData } from '../../common/validation/index.js'
import { createDatabaseService } from '../../common/services/databaseService.js'
import { API_CONFIG } from '../../common/constants/index.js'

// Initialize database service with API configuration
const databaseService = createDatabaseService({
  pocketbaseUrl: CONFIG.POCKETBASE_URL,
  pocketbaseEmail: CONFIG.POCKETBASE_EMAIL,
  pocketbasePassword: CONFIG.POCKETBASE_PASSWORD
})

// Use shared database service directly
function getDatabaseService() {
  return databaseService
}

/**
 * Task Service Class
 * Handles all task-related business logic
 */
export class TaskService {
  /**
   * Create a new task
   * @param {Object} taskData - Task data
   * @returns {Object} Created task
   */
  static async createTask(taskData) {
    try {
      const dbService = await getDatabaseService()
      const sanitizedData = sanitizeData(taskData)

      // Validate project exists
      const project = await dbService.getProjectById(sanitizedData.project_id)
      if (!project) {
        throw errors.NOT_FOUND('Project')
      }

      // Prepare task data with defaults
      const taskToCreate = {
        summary: sanitizedData.summary,
        project_id: sanitizedData.project_id,
        description: sanitizedData.description || null,
        priority: sanitizedData.priority || 'Medium',
        type: sanitizedData.type || 'Task',
        status: sanitizedData.status || 'Backlog',
        estimated_effort: sanitizedData.estimated_effort || 'Medium',
        epic: sanitizedData.epic || null,
        assigned_to: sanitizedData.assigned_to || null,
        linked_tasks: sanitizedData.linked_tasks || []
      }

      // Create task using existing database service
      const createdTask = await dbService.addTask(taskToCreate)
      return createdTask
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to create task: ${error.message}`)
    }
  }

  /**
   * List tasks with optional filtering and pagination
   * @param {Object} filters - Filter parameters
   * @returns {Object} Tasks array with metadata
   */
  static async listTasks(filters = {}) {
    try {
      const dbService = await getDatabaseService()

      // Parse pagination parameters
      const limit = parseInt(filters.limit) || CONFIG.PAGINATION_DEFAULT_LIMIT
      const offset = parseInt(filters.offset) || 0

      // Build filter object for database service
      const dbFilters = {}
      if (filters.project_id) dbFilters.project_id = filters.project_id
      if (filters.status) dbFilters.status = filters.status
      if (filters.priority) dbFilters.priority = filters.priority
      if (filters.type) dbFilters.type = filters.type
      if (filters.epic) dbFilters.epic = filters.epic
      if (filters.assigned_to) dbFilters.assigned_to = filters.assigned_to

      // Get tasks from database
      const tasks = await dbService.getAllTasks(dbFilters)

      // Apply search filtering if provided
      let filteredTasks = tasks
      if (filters.search) {
        const searchLower = filters.search.toLowerCase()
        filteredTasks = tasks.filter(task =>
          task.summary.toLowerCase().includes(searchLower) ||
          (task.description && task.description.toLowerCase().includes(searchLower)) ||
          task.task_id.toLowerCase().includes(searchLower)
        )
      }

      // Apply pagination
      const totalCount = filteredTasks.length
      const paginatedTasks = filteredTasks.slice(offset, offset + limit)

      return {
        tasks: paginatedTasks,
        metadata: {
          total_count: totalCount,
          returned_count: paginatedTasks.length,
          limit: limit,
          offset: offset,
          has_more: offset + limit < totalCount
        }
      }
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to list tasks: ${error.message}`)
    }
  }

  /**
   * Get task by original task ID
   * @param {string} taskId - Original task ID
   * @returns {Object|null} Task object or null
   */
  static async getTaskById(taskId) {
    try {
      const dbService = await getDatabaseService()
      const task = await dbService.getTaskByOriginalId(taskId)
      return task
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to get task: ${error.message}`)
    }
  }

  /**
   * Update an existing task
   * @param {string} taskId - Original task ID
   * @param {Object} updates - Updated task data
   * @returns {Object|null} Updated task or null
   */
  static async updateTask(taskId, updates) {
    try {
      const dbService = await getDatabaseService()
      const sanitizedUpdates = sanitizeData(updates)

      // Get existing task
      const existingTask = await dbService.getTaskByOriginalId(taskId)
      if (!existingTask) {
        throw errors.NOT_FOUND('Task')
      }

      // Validate project if being updated
      if (sanitizedUpdates.project_id && sanitizedUpdates.project_id !== existingTask.project_id) {
        const project = await dbService.getProjectById(sanitizedUpdates.project_id)
        if (!project) {
          throw errors.NOT_FOUND('Project')
        }
      }

      // Update task using existing database service
      const updatedTask = await dbService.updateTask(existingTask.id, sanitizedUpdates)
      return updatedTask
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to update task: ${error.message}`)
    }
  }

  /**
   * Update task status only
   * @param {string} taskId - Original task ID
   * @param {string} status - New status
   * @returns {Object|null} Updated task or null
   */
  static async updateTaskStatus(taskId, status) {
    try {
      const dbService = await getDatabaseService()

      // Get existing task
      const existingTask = await dbService.getTaskByOriginalId(taskId)
      if (!existingTask) {
        throw errors.NOT_FOUND('Task')
      }

      // Update status using existing database service
      const success = await dbService.updateTaskStatus(existingTask.id, status)
      if (!success) {
        throw errors.INTERNAL_SERVER_ERROR('Failed to update task status')
      }

      // Return updated task
      return await dbService.getTaskByOriginalId(taskId)
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to update task status: ${error.message}`)
    }
  }

  /**
   * Delete a task
   * @param {string} taskId - Original task ID
   * @returns {boolean} Success status
   */
  static async deleteTask(taskId) {
    try {
      const dbService = await getDatabaseService()

      // Get existing task
      const existingTask = await dbService.getTaskByOriginalId(taskId)
      if (!existingTask) {
        throw errors.NOT_FOUND('Task')
      }

      // Delete task using existing database service
      const success = await dbService.deleteTask(existingTask.id)
      return success
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to delete task: ${error.message}`)
    }
  }

  /**
   * Search tasks by query
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Object} Search results with metadata
   */
  static async searchTasks(query, filters = {}) {
    try {
      const dbService = await getDatabaseService()

      // Get all tasks first
      const allTasks = await dbService.getAllTasks()

      // Search in summary, description, and task_id
      const searchLower = query.toLowerCase()
      const searchResults = allTasks.filter(task => {
        const matchesSearch = (
          task.summary.toLowerCase().includes(searchLower) ||
          (task.description && task.description.toLowerCase().includes(searchLower)) ||
          task.task_id.toLowerCase().includes(searchLower)
        )

        // Apply additional filters
        if (filters.project_id && task.project_id !== filters.project_id) return false
        if (filters.status && task.status !== filters.status) return false
        if (filters.priority && task.priority !== filters.priority) return false
        if (filters.type && task.type !== filters.type) return false

        return matchesSearch
      })

      // Apply pagination
      const limit = parseInt(filters.limit) || CONFIG.PAGINATION_DEFAULT_LIMIT
      const offset = parseInt(filters.offset) || 0
      const paginatedResults = searchResults.slice(offset, offset + limit)

      return {
        tasks: paginatedResults,
        metadata: {
          total_count: searchResults.length,
          returned_count: paginatedResults.length,
          limit: limit,
          offset: offset,
          has_more: offset + limit < searchResults.length,
          search_query: query
        }
      }
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to search tasks: ${error.message}`)
    }
  }

  /**
   * Get task analytics
   * @param {string} projectId - Optional project ID filter
   * @returns {Object} Task analytics data
   */
  static async getTaskAnalytics(projectId = null) {
    try {
      const dbService = await getDatabaseService()

      // Get statistics
      const statistics = await dbService.getStatistics()

      // If project ID provided, filter for that project
      if (projectId) {
        const projectTasks = await dbService.getTasksByProject(projectId)

        // Calculate project-specific statistics
        const projectStats = {
          totalTasks: projectTasks.length,
          tasksByType: {},
          tasksByPriority: {},
          tasksByStatus: {}
        }

        projectTasks.forEach(task => {
          projectStats.tasksByType[task.type] = (projectStats.tasksByType[task.type] || 0) + 1
          projectStats.tasksByPriority[task.priority] = (projectStats.tasksByPriority[task.priority] || 0) + 1
          projectStats.tasksByStatus[task.status] = (projectStats.tasksByStatus[task.status] || 0) + 1
        })

        return {
          project_id: projectId,
          ...projectStats,
          tasksByType: Object.entries(projectStats.tasksByType).map(([type, count]) => ({ type, count })),
          tasksByPriority: Object.entries(projectStats.tasksByPriority).map(([priority, count]) => ({ priority, count })),
          tasksByStatus: Object.entries(projectStats.tasksByStatus).map(([status, count]) => ({ status, count }))
        }
      }

      return statistics
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to get task analytics: ${error.message}`)
    }
  }

  /**
   * Bulk import tasks
   * @param {string} projectId - Project ID
   * @param {Array} tasks - Tasks to import
   * @returns {Object} Import results
   */
  static async bulkImportTasks(projectId, tasks) {
    try {
      const dbService = await getDatabaseService()

      // Validate project exists
      const project = await dbService.getProjectById(projectId)
      if (!project) {
        throw errors.NOT_FOUND('Project')
      }

      // Use existing bulk import functionality
      const result = await dbService.insertTasksFromJson(tasks, projectId)
      return result
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to bulk import tasks: ${error.message}`)
    }
  }

  /**
   * Bulk update tasks
   * @param {Array} updates - Array of task updates
   * @returns {Object} Update results
   */
  static async bulkUpdateTasks(updates) {
    try {
      const dbService = await getDatabaseService()
      const results = {
        successful: 0,
        failed: 0,
        errors: []
      }

      for (const update of updates) {
        try {
          const { task_id, updates: taskUpdates } = update
          const sanitizedUpdates = sanitizeData(taskUpdates)

          // Get existing task
          const existingTask = await dbService.getTaskByOriginalId(task_id)
          if (!existingTask) {
            results.failed++
            results.errors.push(`Task not found: ${task_id}`)
            continue
          }

          // Update task
          await dbService.updateTask(existingTask.id, sanitizedUpdates)
          results.successful++
        } catch (error) {
          results.failed++
          results.errors.push(`Failed to update task ${update.task_id}: ${error.message}`)
        }
      }

      return results
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to bulk update tasks: ${error.message}`)
    }
  }

  /**
   * Bulk delete tasks
   * @param {Array} taskIds - Array of task IDs to delete
   * @returns {Object} Delete results
   */
  static async bulkDeleteTasks(taskIds) {
    try {
      const dbService = await getDatabaseService()
      const results = {
        successful: 0,
        failed: 0,
        errors: []
      }

      for (const taskId of taskIds) {
        try {
          // Get existing task
          const existingTask = await dbService.getTaskByOriginalId(taskId)
          if (!existingTask) {
            results.failed++
            results.errors.push(`Task not found: ${taskId}`)
            continue
          }

          // Delete task
          const success = await dbService.deleteTask(existingTask.id)
          if (success) {
            results.successful++
          } else {
            results.failed++
            results.errors.push(`Failed to delete task: ${taskId}`)
          }
        } catch (error) {
          results.failed++
          results.errors.push(`Failed to delete task ${taskId}: ${error.message}`)
        }
      }

      return results
    } catch (error) {
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to bulk delete tasks: ${error.message}`)
    }
  }
}
