/**
 * Session Management Service
 * Handles server-side session tracking and invalidation
 */

import { errors } from '../middleware/errorHandler.js'

/**
 * Token blacklist storage
 * In production, this should use Redis or a database
 */
const tokenBlacklist = new Set()
const sessionStore = new Map()

/**
 * Session data structure
 */
class Session {
  constructor(userId, token, userAgent, ip) {
    this.id = this.generateSessionId()
    this.userId = userId
    this.token = token
    this.userAgent = userAgent
    this.ip = ip
    this.createdAt = new Date()
    this.lastActivity = new Date()
    this.isActive = true
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
  }

  updateActivity() {
    this.lastActivity = new Date()
  }

  invalidate() {
    this.isActive = false
  }
}

/**
 * Session Service Class
 */
export class SessionService {
  /**
   * Create a new session
   * @param {string} userId - User ID
   * @param {string} token - Authentication token
   * @param {string} userAgent - User agent string
   * @param {string} ip - Client IP address
   * @returns {Session} Created session
   */
  static createSession(userId, token, userAgent, ip) {
    const session = new Session(userId, token, userAgent, ip)
    sessionStore.set(session.id, session)
    
    // Also store by token for quick lookup
    sessionStore.set(`token:${token}`, session.id)
    
    return session
  }

  /**
   * Get session by ID
   * @param {string} sessionId - Session ID
   * @returns {Session|null} Session object or null
   */
  static getSession(sessionId) {
    return sessionStore.get(sessionId) || null
  }

  /**
   * Get session by token
   * @param {string} token - Authentication token
   * @returns {Session|null} Session object or null
   */
  static getSessionByToken(token) {
    const sessionId = sessionStore.get(`token:${token}`)
    return sessionId ? sessionStore.get(sessionId) : null
  }

  /**
   * Get all sessions for a user
   * @param {string} userId - User ID
   * @returns {Session[]} Array of user sessions
   */
  static getUserSessions(userId) {
    const sessions = []
    for (const session of sessionStore.values()) {
      if (session instanceof Session && session.userId === userId && session.isActive) {
        sessions.push(session)
      }
    }
    return sessions
  }

  /**
   * Update session activity
   * @param {string} token - Authentication token
   * @returns {boolean} Success status
   */
  static updateSessionActivity(token) {
    const session = this.getSessionByToken(token)
    if (session && session.isActive) {
      session.updateActivity()
      return true
    }
    return false
  }

  /**
   * Invalidate a specific session
   * @param {string} sessionId - Session ID
   * @returns {boolean} Success status
   */
  static invalidateSession(sessionId) {
    const session = sessionStore.get(sessionId)
    if (session) {
      session.invalidate()
      // Add token to blacklist
      tokenBlacklist.add(session.token)
      return true
    }
    return false
  }

  /**
   * Invalidate session by token
   * @param {string} token - Authentication token
   * @returns {boolean} Success status
   */
  static invalidateSessionByToken(token) {
    const session = this.getSessionByToken(token)
    if (session) {
      session.invalidate()
      tokenBlacklist.add(token)
      return true
    }
    return false
  }

  /**
   * Invalidate all sessions for a user
   * @param {string} userId - User ID
   * @param {string} excludeToken - Token to exclude from invalidation (current session)
   * @returns {number} Number of sessions invalidated
   */
  static invalidateAllUserSessions(userId, excludeToken = null) {
    const userSessions = this.getUserSessions(userId)
    let invalidatedCount = 0

    for (const session of userSessions) {
      if (session.token !== excludeToken) {
        session.invalidate()
        tokenBlacklist.add(session.token)
        invalidatedCount++
      }
    }

    return invalidatedCount
  }

  /**
   * Check if a token is blacklisted
   * @param {string} token - Authentication token
   * @returns {boolean} True if token is blacklisted
   */
  static isTokenBlacklisted(token) {
    return tokenBlacklist.has(token)
  }

  /**
   * Clean up expired sessions
   * @param {number} maxAge - Maximum session age in milliseconds (default: 24 hours)
   * @returns {number} Number of sessions cleaned up
   */
  static cleanupExpiredSessions(maxAge = 24 * 60 * 60 * 1000) {
    const now = new Date()
    let cleanedCount = 0

    for (const [key, value] of sessionStore.entries()) {
      if (value instanceof Session) {
        const sessionAge = now - value.lastActivity
        if (sessionAge > maxAge || !value.isActive) {
          // Remove from session store
          sessionStore.delete(key)
          sessionStore.delete(`token:${value.token}`)
          
          // Add to blacklist if not already there
          tokenBlacklist.add(value.token)
          cleanedCount++
        }
      }
    }

    return cleanedCount
  }

  /**
   * Get session statistics
   * @returns {Object} Session statistics
   */
  static getSessionStats() {
    let activeSessions = 0
    let totalSessions = 0
    const userSessionCounts = new Map()

    for (const value of sessionStore.values()) {
      if (value instanceof Session) {
        totalSessions++
        if (value.isActive) {
          activeSessions++
          const count = userSessionCounts.get(value.userId) || 0
          userSessionCounts.set(value.userId, count + 1)
        }
      }
    }

    return {
      activeSessions,
      totalSessions,
      blacklistedTokens: tokenBlacklist.size,
      uniqueUsers: userSessionCounts.size,
      averageSessionsPerUser: userSessionCounts.size > 0 ? 
        activeSessions / userSessionCounts.size : 0
    }
  }

  /**
   * Force logout a user (admin function)
   * @param {string} userId - User ID to force logout
   * @returns {Object} Result with number of sessions invalidated
   */
  static forceLogoutUser(userId) {
    const invalidatedCount = this.invalidateAllUserSessions(userId)
    
    return {
      success: true,
      message: `Forced logout for user ${userId}`,
      sessionsInvalidated: invalidatedCount
    }
  }

  /**
   * Get detailed session information for a user
   * @param {string} userId - User ID
   * @returns {Object[]} Array of session details
   */
  static getUserSessionDetails(userId) {
    const sessions = this.getUserSessions(userId)
    
    return sessions.map(session => ({
      id: session.id,
      createdAt: session.createdAt,
      lastActivity: session.lastActivity,
      userAgent: session.userAgent,
      ip: session.ip,
      isActive: session.isActive,
      isCurrent: false // This should be set by the calling code
    }))
  }

  /**
   * Validate session and update activity
   * @param {string} token - Authentication token
   * @returns {Object} Validation result
   */
  static validateSession(token) {
    // Check if token is blacklisted
    if (this.isTokenBlacklisted(token)) {
      return {
        valid: false,
        reason: 'Token is blacklisted'
      }
    }

    // Get session
    const session = this.getSessionByToken(token)
    if (!session) {
      return {
        valid: false,
        reason: 'Session not found'
      }
    }

    // Check if session is active
    if (!session.isActive) {
      return {
        valid: false,
        reason: 'Session is inactive'
      }
    }

    // Update activity
    session.updateActivity()

    return {
      valid: true,
      session: {
        id: session.id,
        userId: session.userId,
        lastActivity: session.lastActivity
      }
    }
  }
}

// Cleanup expired sessions every hour
setInterval(() => {
  const cleaned = SessionService.cleanupExpiredSessions()
  if (cleaned > 0) {
    console.log(`Cleaned up ${cleaned} expired sessions`)
  }
}, 60 * 60 * 1000)
