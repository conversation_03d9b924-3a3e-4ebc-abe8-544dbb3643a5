/**
 * Test Setup for API Module
 * Sets up test environment and utilities
 */

import { beforeEach, afterEach } from 'vitest'

// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.API_PORT = '3002'
process.env.VITE_POCKETBASE_URL = 'http://localhost:8090'

// Global test setup
beforeEach(() => {
  // Reset any global state before each test
})

afterEach(() => {
  // Cleanup after each test
})
