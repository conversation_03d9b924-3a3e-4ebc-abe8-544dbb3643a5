/**
 * API Server Entry Point
 * Initializes and starts the Express.js API server with specified routes and middleware
 */

import { createApp, CONFIG } from './config/server.js'
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js'
import { taskRoutes } from './routes/tasks.js'
import { projectRoutes } from './routes/projects.js'

const app = createApp()

// Register API routes
app.use(`${CONFIG.API_PREFIX}/tasks`, taskRoutes)
app.use(`${CONFIG.API_PREFIX}/projects`, projectRoutes)

// 404 handler for undefined routes
app.use(notFoundHandler)

// Global error handler
app.use(errorHandler)

// Start API server
app.listen(CONFIG.PORT, () => {
  console.log(`API server running at http://localhost:${CONFIG.PORT}${CONFIG.API_PREFIX}`)
})

