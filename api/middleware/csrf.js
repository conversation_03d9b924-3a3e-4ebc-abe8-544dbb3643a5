/**
 * CSRF Protection Middleware
 * Implements Cross-Site Request Forgery protection for authentication endpoints
 */

import crypto from 'crypto'
import { errors } from './errorHandler.js'

/**
 * CSRF token storage (in production, use Redis or database)
 * Maps session ID to CSRF token
 */
const csrfTokens = new Map()

/**
 * Generate a secure CSRF token
 * @returns {string} CSRF token
 */
function createCSRFToken() {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * Get session ID from request (using request ID as session identifier)
 * In production, this should use actual session management
 * @param {Request} req - Express request object
 * @returns {string} Session identifier
 */
function getSessionId(req) {
  // Use request ID as temporary session identifier
  // In production, use proper session management
  return req.requestId || req.ip + req.headers['user-agent']
}

/**
 * CSRF token generation middleware
 * Generates and stores CSRF tokens for authenticated sessions
 */
export const generateCSRFToken = (req, res, next) => {
  try {
    const sessionId = getSessionId(req)
    const token = createCSRFToken()

    // Store token with expiration (1 hour)
    csrfTokens.set(sessionId, {
      token,
      expires: Date.now() + (60 * 60 * 1000) // 1 hour
    })

    // Set CSRF token in secure cookie
    res.cookie('csrf-token', token, {
      httpOnly: false, // Allow JavaScript access for AJAX requests
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      sameSite: 'strict',
      maxAge: 60 * 60 * 1000 // 1 hour
    })

    // Also provide token in response header for SPA usage
    res.setHeader('X-CSRF-Token', token)

    next()
  } catch (error) {
    next(errors.INTERNAL_SERVER_ERROR('Failed to generate CSRF token'))
  }
}

/**
 * CSRF token validation middleware
 * Validates CSRF tokens for state-changing operations
 */
export const validateCSRFToken = (req, res, next) => {
  try {
    // Skip CSRF validation for GET, HEAD, OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next()
    }

    const sessionId = getSessionId(req)
    const tokenFromHeader = req.headers['x-csrf-token']
    const tokenFromBody = req.body?.csrfToken
    const tokenFromCookie = req.cookies?.['csrf-token']

    // Get token from header, body, or cookie
    const providedToken = tokenFromHeader || tokenFromBody || tokenFromCookie

    if (!providedToken) {
      return next(errors.FORBIDDEN('CSRF token missing'))
    }

    // Get stored token
    const storedTokenData = csrfTokens.get(sessionId)

    if (!storedTokenData) {
      return next(errors.FORBIDDEN('CSRF token not found for session'))
    }

    // Check if token has expired
    if (Date.now() > storedTokenData.expires) {
      csrfTokens.delete(sessionId)
      return next(errors.FORBIDDEN('CSRF token expired'))
    }

    // Validate token
    if (providedToken !== storedTokenData.token) {
      return next(errors.FORBIDDEN('Invalid CSRF token'))
    }

    next()
  } catch (error) {
    next(errors.INTERNAL_SERVER_ERROR('CSRF validation failed'))
  }
}

/**
 * CSRF protection for authentication routes
 * Applies CSRF validation to specific authentication endpoints
 */
export const csrfProtection = (req, res, next) => {
  // Apply CSRF protection to state-changing authentication operations
  const protectedPaths = [
    '/login',
    '/register',
    '/logout',
    '/forgot-password',
    '/reset-password',
    '/profile',
    '/password'
  ]

  const isProtectedPath = protectedPaths.some(path =>
    req.path.endsWith(path) || req.path.includes(path)
  )

  if (isProtectedPath && !['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return validateCSRFToken(req, res, next)
  }

  next()
}

/**
 * Cleanup expired CSRF tokens
 * Should be called periodically to prevent memory leaks
 */
export const cleanupExpiredTokens = () => {
  const now = Date.now()
  for (const [sessionId, tokenData] of csrfTokens.entries()) {
    if (now > tokenData.expires) {
      csrfTokens.delete(sessionId)
    }
  }
}

/**
 * Get CSRF token for current session
 * Utility function for retrieving current CSRF token
 */
export const getCSRFToken = (req) => {
  const sessionId = getSessionId(req)
  const tokenData = csrfTokens.get(sessionId)
  return tokenData && Date.now() <= tokenData.expires ? tokenData.token : null
}

/**
 * CSRF token endpoint
 * Provides CSRF token for AJAX requests
 */
export const csrfTokenEndpoint = (req, res) => {
  try {
    const token = getCSRFToken(req)

    if (!token) {
      // Generate new token if none exists
      generateCSRFToken(req, res, () => {
        const newToken = getCSRFToken(req)
        res.json({
          success: true,
          data: {
            csrfToken: newToken
          }
        })
      })
    } else {
      res.json({
        success: true,
        data: {
          csrfToken: token
        }
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to retrieve CSRF token',
        timestamp: new Date().toISOString()
      }
    })
  }
}

// Cleanup expired tokens every 15 minutes
setInterval(cleanupExpiredTokens, 15 * 60 * 1000)
