/**
 * Error Handling Middleware
 * Standardizes error responses across the API
 */

import { CONFIG } from '../config/server.js'

/**
 * Custom API Error class
 */
export class APIError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_SERVER_ERROR', details = null) {
    super(message)
    this.name = 'APIError'
    this.statusCode = statusCode
    this.code = code
    this.details = details
    this.timestamp = new Date().toISOString()
  }
}

/**
 * Create standardized error response
 * @param {Error} error - The error object
 * @param {string} requestId - Request ID for debugging
 * @returns {Object} Standardized error response
 */
function createErrorResponse(error, requestId) {
  const isProduction = CONFIG.NODE_ENV === 'production'
  
  const errorResponse = {
    error: {
      code: error.code || 'INTERNAL_SERVER_ERROR',
      message: error.message || 'An unexpected error occurred',
      timestamp: error.timestamp || new Date().toISOString(),
      request_id: requestId
    }
  }

  // Add details if available and not in production
  if (error.details) {
    errorResponse.error.details = error.details
  }

  // Add stack trace in development
  if (!isProduction && error.stack) {
    errorResponse.error.stack = error.stack
  }

  return errorResponse
}

/**
 * Global error handler middleware
 * @param {Error} err - Error object
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function errorHandler(err, req, res, next) {
  // If response already sent, delegate to default Express error handler
  if (res.headersSent) {
    return next(err)
  }

  let statusCode = 500
  let apiError = err

  // Handle different error types
  if (err instanceof APIError) {
    statusCode = err.statusCode
  } else if (err.name === 'ValidationError') {
    statusCode = 400
    apiError = new APIError(
      'Validation failed',
      400,
      'VALIDATION_ERROR',
      err.details || err.message
    )
  } else if (err.name === 'CastError') {
    statusCode = 400
    apiError = new APIError(
      'Invalid ID format',
      400,
      'INVALID_ID',
      `Invalid ${err.path}: ${err.value}`
    )
  } else if (err.code === 11000) {
    statusCode = 409
    apiError = new APIError(
      'Duplicate entry',
      409,
      'DUPLICATE_ENTRY',
      'A record with this value already exists'
    )
  } else if (err.name === 'JsonWebTokenError') {
    statusCode = 401
    apiError = new APIError(
      'Invalid token',
      401,
      'INVALID_TOKEN'
    )
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401
    apiError = new APIError(
      'Token expired',
      401,
      'TOKEN_EXPIRED'
    )
  } else {
    // Generic error
    apiError = new APIError(
      CONFIG.NODE_ENV === 'production' ? 'Internal server error' : err.message,
      500,
      'INTERNAL_SERVER_ERROR'
    )
  }

  // Log error for debugging
  console.error(`[${req.requestId}] Error:`, {
    error: err.message,
    stack: err.stack,
    statusCode,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  })

  // Send error response
  res.status(statusCode).json(createErrorResponse(apiError, req.requestId))
}

/**
 * 404 handler middleware
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function notFoundHandler(req, res, next) {
  const error = new APIError(
    `Route ${req.method} ${req.originalUrl} not found`,
    404,
    'NOT_FOUND'
  )
  next(error)
}

/**
 * Async error wrapper
 * Wraps async route handlers to catch errors
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
export function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

/**
 * Common API errors
 */
export const errors = {
  UNAUTHORIZED: (message = 'Unauthorized') => 
    new APIError(message, 401, 'UNAUTHORIZED'),
  
  FORBIDDEN: (message = 'Forbidden') => 
    new APIError(message, 403, 'FORBIDDEN'),
  
  NOT_FOUND: (resource = 'Resource') => 
    new APIError(`${resource} not found`, 404, 'NOT_FOUND'),
  
  VALIDATION_ERROR: (details) => 
    new APIError('Validation failed', 400, 'VALIDATION_ERROR', details),
  
  CONFLICT: (message = 'Conflict') => 
    new APIError(message, 409, 'CONFLICT'),
  
  UNPROCESSABLE_ENTITY: (message = 'Unprocessable entity') => 
    new APIError(message, 422, 'UNPROCESSABLE_ENTITY'),
  
  INTERNAL_SERVER_ERROR: (message = 'Internal server error') => 
    new APIError(message, 500, 'INTERNAL_SERVER_ERROR')
}
