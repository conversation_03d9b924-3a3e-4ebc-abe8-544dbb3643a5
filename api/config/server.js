/**
 * API Server Configuration
 * Sets up Express.js server with middleware and security configurations
 */

import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import rateLimit from 'express-rate-limit'
import cookieParser from 'cookie-parser'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import { applySecurity } from '../middleware/security.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

/**
 * Create and configure Express application
 * @returns {Express} Configured Express app
 */
export function createApp() {
  const app = express()

  // Apply security middleware first
  applySecurity.forEach(middleware => app.use(middleware))

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: false, // Disable CSP for API
    crossOriginEmbedderPolicy: false
  }))

  // CORS configuration
  app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token']
  }))

  // Cookie parsing middleware
  app.use(cookieParser())

  // Rate limiting
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests from this IP, please try again later.',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false
  })

  app.use('/api/', limiter)

  // Compression
  app.use(compression())

  // Body parsing middleware
  app.use(express.json({ limit: '1mb' }))
  app.use(express.urlencoded({ extended: true, limit: '1mb' }))

  // Logging middleware
  if (process.env.NODE_ENV !== 'test') {
    app.use(morgan('combined'))
  }

  // Add request ID for debugging
  app.use((req, res, next) => {
    req.requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    res.setHeader('X-Request-ID', req.requestId)
    next()
  })

  return app
}

import { ENV_KEYS, SIZE_LIMITS, API_CONFIG } from '../../common/constants/index.js'

/**
 * Server configuration constants
 */
export const CONFIG = {
  PORT: process.env[ENV_KEYS.API_PORT] || 3001,
  NODE_ENV: process.env[ENV_KEYS.NODE_ENV] || 'development',
  API_PREFIX: '/api',
  VERSION: '1.0.0',

  // Request limits
  MAX_FILE_SIZE: SIZE_LIMITS.MAX_FILE_SIZE,
  MAX_JSON_SIZE: SIZE_LIMITS.MAX_JSON_SIZE,

  // Database configuration
  POCKETBASE_URL: process.env[ENV_KEYS.POCKETBASE_URL] || 'http://localhost:8090',
  POCKETBASE_EMAIL: process.env[ENV_KEYS.POCKETBASE_EMAIL],
  POCKETBASE_PASSWORD: process.env[ENV_KEYS.POCKETBASE_PASSWORD],

  // Security
  API_KEY_HEADER: 'X-API-Key',
  JWT_SECRET: process.env[ENV_KEYS.JWT_SECRET] || 'your-secret-key-change-in-production',

  // Performance
  CACHE_TTL: 300, // 5 minutes
  PAGINATION_DEFAULT_LIMIT: API_CONFIG.DEFAULT_PAGINATION_LIMIT,
  PAGINATION_MAX_LIMIT: API_CONFIG.MAX_PAGINATION_LIMIT
}
